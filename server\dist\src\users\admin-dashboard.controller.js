"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDashboardController = void 0;
const common_1 = require("@nestjs/common");
const admin_dashboard_service_1 = require("./admin-dashboard.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const client_1 = require("@prisma/client");
let AdminDashboardController = class AdminDashboardController {
    constructor(dashboardService) {
        this.dashboardService = dashboardService;
    }
    async getDashboardOverview() {
        const [userStats, activityMetrics, systemHealth, performanceMetrics,] = await Promise.all([
            this.dashboardService.getUserStatistics(),
            this.dashboardService.getActivityMetrics(),
            this.dashboardService.getSystemHealth(),
            this.dashboardService.getPerformanceMetrics(),
        ]);
        return {
            userStats,
            activityMetrics,
            systemHealth,
            performanceMetrics,
        };
    }
    async getUserStatistics() {
        return this.dashboardService.getUserStatistics();
    }
    async getActivityMetrics(startDate, endDate) {
        return this.dashboardService.getActivityMetrics(startDate, endDate);
    }
    async getSystemHealth() {
        return this.dashboardService.getSystemHealth();
    }
    async getPerformanceMetrics() {
        return this.dashboardService.getPerformanceMetrics();
    }
    async getReviewStatistics(startDate, endDate) {
        const metrics = await this.dashboardService.getActivityMetrics(startDate, endDate);
        return metrics.reviewActivity;
    }
    async getApprovalStatistics(startDate, endDate) {
        const metrics = await this.dashboardService.getActivityMetrics(startDate, endDate);
        return metrics.approvalActivity;
    }
    async getActiveUsers(period = 'daily', startDate, endDate) {
        const metrics = await this.dashboardService.getActivityMetrics(startDate, endDate);
        return metrics.loginActivity;
    }
    async getApplicationThroughput(period = 'daily', startDate, endDate) {
        const metrics = await this.dashboardService.getPerformanceMetrics();
        return metrics.applicationThroughput;
    }
    async getUserProductivity(startDate, endDate, department, role) {
        const metrics = await this.dashboardService.getPerformanceMetrics();
        return metrics.userProductivity;
    }
    async getSystemErrors(startDate, endDate, severity) {
        const health = await this.dashboardService.getSystemHealth();
        return health.recentErrors;
    }
    async getDepartmentPerformance(startDate, endDate) {
        const metrics = await this.dashboardService.getPerformanceMetrics();
        return {
            reviewTimes: metrics.averageReviewTime,
            approvalTimes: metrics.averageApprovalTime,
            throughput: metrics.applicationThroughput,
        };
    }
    async getRoleActivity(startDate, endDate) {
        const [userStats, activityMetrics] = await Promise.all([
            this.dashboardService.getUserStatistics(),
            this.dashboardService.getActivityMetrics(startDate, endDate),
        ]);
        return {
            distribution: userStats.roleDistribution,
            activity: activityMetrics.userActions,
        };
    }
    async getNotificationStatistics(startDate, endDate) {
        return {};
    }
    async getAuditSummary(startDate, endDate) {
        return {};
    }
};
exports.AdminDashboardController = AdminDashboardController;
__decorate([
    (0, common_1.Get)('overview'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getDashboardOverview", null);
__decorate([
    (0, common_1.Get)('users/statistics'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getUserStatistics", null);
__decorate([
    (0, common_1.Get)('activity/metrics'),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getActivityMetrics", null);
__decorate([
    (0, common_1.Get)('system/health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getSystemHealth", null);
__decorate([
    (0, common_1.Get)('performance/metrics'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getPerformanceMetrics", null);
__decorate([
    (0, common_1.Get)('reviews/statistics'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getReviewStatistics", null);
__decorate([
    (0, common_1.Get)('approvals/statistics'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getApprovalStatistics", null);
__decorate([
    (0, common_1.Get)('users/active'),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(2, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getActiveUsers", null);
__decorate([
    (0, common_1.Get)('applications/throughput'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(2, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getApplicationThroughput", null);
__decorate([
    (0, common_1.Get)('users/productivity'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __param(2, (0, common_1.Query)('department')),
    __param(3, (0, common_1.Query)('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date, String, String]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getUserProductivity", null);
__decorate([
    (0, common_1.Get)('system/errors'),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __param(2, (0, common_1.Query)('severity')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date, String]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getSystemErrors", null);
__decorate([
    (0, common_1.Get)('departments/performance'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getDepartmentPerformance", null);
__decorate([
    (0, common_1.Get)('roles/activity'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getRoleActivity", null);
__decorate([
    (0, common_1.Get)('notifications/statistics'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getNotificationStatistics", null);
__decorate([
    (0, common_1.Get)('audit/summary'),
    (0, common_1.UseInterceptors)(common_1.CacheInterceptor),
    (0, common_1.CacheTTL)(300),
    __param(0, (0, common_1.Query)('startDate', common_1.ParseDatePipe)),
    __param(1, (0, common_1.Query)('endDate', common_1.ParseDatePipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date]),
    __metadata("design:returntype", Promise)
], AdminDashboardController.prototype, "getAuditSummary", null);
exports.AdminDashboardController = AdminDashboardController = __decorate([
    (0, common_1.Controller)('admin/dashboard'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_guard_1.Roles)(client_1.UserRole.ADMIN),
    __metadata("design:paramtypes", [admin_dashboard_service_1.AdminDashboardService])
], AdminDashboardController);
//# sourceMappingURL=admin-dashboard.controller.js.map
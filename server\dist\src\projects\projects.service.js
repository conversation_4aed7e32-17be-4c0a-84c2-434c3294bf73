"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let ProjectsService = class ProjectsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createProjectDto) {
        try {
            await this.validateRelationships(createProjectDto);
            const existingProject = await this.prisma.project.findUnique({
                where: { projectDocumentId: createProjectDto.projectDocumentId },
            });
            if (existingProject) {
                throw new common_1.ConflictException(`Document with ID ${createProjectDto.projectDocumentId} is already used by another project`);
            }
            const project = await this.prisma.project.create({
                data: createProjectDto,
                include: {
                    budgetType: true,
                    fundingUnit: true,
                    fundingSource: true,
                    organization: true,
                    projectDocument: true,
                    mouApplication: true,
                    activities: true,
                    approvalSteps: {
                        include: {
                            reviewer: true,
                        },
                    },
                },
            });
            return project;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Project document must be unique');
                }
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async findAll(page = 1, limit = 10, mouApplicationId, organizationId) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(mouApplicationId && { mouApplicationId }),
            ...(organizationId && { organizationId }),
        };
        const [projects, total] = await Promise.all([
            this.prisma.project.findMany({
                where,
                skip,
                take: limit,
                include: {
                    budgetType: true,
                    fundingUnit: true,
                    fundingSource: true,
                    organization: true,
                    projectDocument: true,
                    mouApplication: true,
                    activities: true,
                    approvalSteps: {
                        include: {
                            reviewer: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.project.count({ where }),
        ]);
        return {
            data: projects,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const project = await this.prisma.project.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                budgetType: true,
                fundingUnit: true,
                fundingSource: true,
                organization: true,
                projectDocument: true,
                mouApplication: {
                    include: {
                        mou: {
                            include: {
                                party: true,
                            },
                        },
                    },
                },
                activities: {
                    include: {
                        domainIntervention: true,
                        input: {
                            include: {
                                inputSubclass: true,
                            },
                        },
                    },
                },
                approvalSteps: {
                    include: {
                        reviewer: true,
                    },
                },
            },
        });
        if (!project) {
            throw new common_1.NotFoundException(`Project with ID ${id} not found`);
        }
        return project;
    }
    async update(id, updateProjectDto) {
        const existingProject = await this.prisma.project.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingProject) {
            throw new common_1.NotFoundException(`Project with ID ${id} not found`);
        }
        if (Object.keys(updateProjectDto).some(key => ['budgetTypeId', 'fundingUnitId', 'fundingSourceId', 'organizationId', 'projectDocumentId'].includes(key))) {
            await this.validateRelationships({
                ...existingProject,
                ...updateProjectDto,
            });
        }
        if (updateProjectDto.projectDocumentId && updateProjectDto.projectDocumentId !== existingProject.projectDocumentId) {
            const documentInUse = await this.prisma.project.findUnique({
                where: { projectDocumentId: updateProjectDto.projectDocumentId },
            });
            if (documentInUse) {
                throw new common_1.ConflictException(`Document with ID ${updateProjectDto.projectDocumentId} is already used by another project`);
            }
        }
        try {
            const updatedProject = await this.prisma.project.update({
                where: { id },
                data: updateProjectDto,
                include: {
                    budgetType: true,
                    fundingUnit: true,
                    fundingSource: true,
                    organization: true,
                    projectDocument: true,
                    mouApplication: true,
                    activities: true,
                    approvalSteps: {
                        include: {
                            reviewer: true,
                        },
                    },
                },
            });
            return updatedProject;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Project document must be unique');
                }
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingProject = await this.prisma.project.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingProject) {
            throw new common_1.NotFoundException(`Project with ID ${id} not found`);
        }
        await this.prisma.project.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Project deleted successfully' };
    }
    async getProjectsByMouApplication(mouApplicationId) {
        const projects = await this.prisma.project.findMany({
            where: {
                mouApplicationId,
                deleted: false,
            },
            include: {
                budgetType: true,
                fundingUnit: true,
                fundingSource: true,
                organization: true,
                projectDocument: true,
                activities: true,
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        return projects;
    }
    async validateRelationships(dto) {
        const [budgetType, fundingUnit, fundingSource, organization, document, mouApplication] = await Promise.all([
            this.prisma.budgetType.findUnique({ where: { id: dto.budgetTypeId } }),
            this.prisma.fundingUnit.findUnique({ where: { id: dto.fundingUnitId } }),
            this.prisma.fundingSource.findUnique({ where: { id: dto.fundingSourceId } }),
            this.prisma.organization.findUnique({ where: { id: dto.organizationId } }),
            this.prisma.document.findUnique({ where: { id: dto.projectDocumentId } }),
            this.prisma.mouApplication.findUnique({ where: { id: dto.mouApplicationId } }),
        ]);
        if (!budgetType) {
            throw new common_1.NotFoundException(`Budget type with ID ${dto.budgetTypeId} not found`);
        }
        if (!fundingUnit) {
            throw new common_1.NotFoundException(`Funding unit with ID ${dto.fundingUnitId} not found`);
        }
        if (!fundingSource) {
            throw new common_1.NotFoundException(`Funding source with ID ${dto.fundingSourceId} not found`);
        }
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${dto.organizationId} not found`);
        }
        if (!document) {
            throw new common_1.NotFoundException(`Document with ID ${dto.projectDocumentId} not found`);
        }
        if (!mouApplication) {
            throw new common_1.NotFoundException(`MoU application with ID ${dto.mouApplicationId} not found`);
        }
    }
};
exports.ProjectsService = ProjectsService;
exports.ProjectsService = ProjectsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ProjectsService);
//# sourceMappingURL=projects.service.js.map
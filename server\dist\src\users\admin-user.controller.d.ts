import { AdminUserService } from './admin-user.service';
import { UserResponse, AuditLogResponse } from './interfaces/user.interface';
import { CreateUserDto, UpdateUserDto, AssignRolesDto, FilterUsersDto, UserActivityDto, BulkUserActionDto } from './dto/admin-user.dto';
export declare class AdminUserController {
    private readonly adminUserService;
    constructor(adminUserService: AdminUserService);
    createUser(createUserDto: CreateUserDto): Promise<UserResponse>;
    findAll(filters: FilterUsersDto): Promise<{
        data: Array<Omit<import("@prisma/client").User, "password">>;
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<Omit<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        refreshToken: string | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        isActive: boolean;
        organizationId: number | null;
    }, "password">>;
    updateUser(id: number, updateUserDto: UpdateUserDto): Promise<Omit<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        refreshToken: string | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        isActive: boolean;
        organizationId: number | null;
    }, "password">>;
    assignRoles(id: number, assignRolesDto: AssignRolesDto): Promise<UserResponse>;
    getUserActivity(id: number, activityDto: UserActivityDto): Promise<{
        data: Array<import("@prisma/client").ActivityLog & {
            user: Pick<import("@prisma/client").User, "firstName" | "lastName" | "email">;
        }>;
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    bulkUpdateUsers(bulkActionDto: BulkUserActionDto): Promise<Omit<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        refreshToken: string | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        isActive: boolean;
        organizationId: number | null;
    }, "password">[]>;
    resetUserPassword(id: number, newPassword: string): Promise<{
        message: string;
    }>;
    deactivateUser(id: number): Promise<Omit<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        refreshToken: string | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        isActive: boolean;
        organizationId: number | null;
    }, "password">>;
    reactivateUser(id: number): Promise<Omit<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        firstName: string;
        lastName: string;
        email: string;
        password: string;
        emailVerified: boolean;
        verifiedAt: Date | null;
        verificationToken: string | null;
        verificationTokenExpiryTime: Date | null;
        refreshToken: string | null;
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        invitationToken: string | null;
        invitationExpires: Date | null;
        invitedBy: string | null;
        role: import("@prisma/client").$Enums.UserRole;
        isActive: boolean;
        organizationId: number | null;
    }, "password">>;
    getUserRoleStatistics(): Promise<{}>;
    getActivityStatistics(): Promise<{}>;
    getDepartments(): Promise<{}>;
    getOrganizations(): Promise<{}>;
    importUsers(usersData: any): Promise<{}>;
    exportUsers(filters: FilterUsersDto): Promise<{}>;
    assignOrganization(id: number, organizationId: number): Promise<{}>;
    assignDepartment(id: number, department: string): Promise<{}>;
    getAuditLog(filters: AuditLogFilterDto): Promise<AuditLogResponse>;
    notifyUsers(notification: any): Promise<{}>;
    getPermissions(): Promise<{}>;
    updateUserPermissions(id: number, permissions: UpdatePermissionsDto): Promise<UserResponse>;
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AdminUserService } from './admin-user.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard, Roles } from '../auth/guards/roles.guard';
import { Role, BaseRole, ReviewerRole, ApproverRole } from '../common/enums/roles.enum';
import { UserResponse, UserListResponse, ActivityResponse, AuditLogResponse } from './interfaces/user.interface';
import {
  CreateUserDto,
  UpdateUserDto,
  AssignRolesDto,
  FilterUsersDto,
  UserActivityDto,
  BulkUserActionDto,
} from './dto/admin-user.dto';

@Controller('admin/users')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(BaseRole.ADMIN)
@ApiTags('User Administration')
export class AdminUserController {
  constructor(private readonly adminUserService: AdminUserService) {}

  @Post()
  @ApiOperation({ summary: 'Create new user' })
  @ApiResponse({ status: 201, type: UserResponse })
  async createUser(@Body() createUserDto: CreateUserDto): Promise<UserResponse> {
    return this.adminUserService.createUser(createUserDto);
  }

  @Get()
  async findAll(@Query() filters: FilterUsersDto) {
    return this.adminUserService.findAllUsers(filters);
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.adminUserService.findOne(id);
  }

  @Patch(':id')
  async updateUser(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.adminUserService.updateUser(id, updateUserDto);
  }

  @Patch(':id/roles')
  @ApiOperation({ summary: 'Assign roles to user' })
  @ApiResponse({ status: 200, type: UserResponse })
  @Roles(BaseRole.ADMIN, ApproverRole.ROLE_MANAGER)
  async assignRoles(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignRolesDto: AssignRolesDto,
  ): Promise<UserResponse> {
    return this.adminUserService.assignRoles(id, assignRolesDto);
  }

  @Get(':id/activity')
  async getUserActivity(
    @Param('id', ParseIntPipe) id: number,
    @Query() activityDto: UserActivityDto,
  ) {
    activityDto.userId = id;
    return this.adminUserService.getUserActivity(activityDto);
  }

  @Post('bulk-update')
  @HttpCode(HttpStatus.OK)
  async bulkUpdateUsers(@Body() bulkActionDto: BulkUserActionDto) {
    return this.adminUserService.bulkUpdateUsers(bulkActionDto);
  }

  @Post(':id/reset-password')
  @HttpCode(HttpStatus.OK)
  async resetUserPassword(
    @Param('id', ParseIntPipe) id: number,
    @Body('newPassword') newPassword: string,
  ) {
    return this.adminUserService.resetUserPassword(id, newPassword);
  }

  @Patch(':id/deactivate')
  async deactivateUser(@Param('id', ParseIntPipe) id: number) {
    return this.adminUserService.deactivateUser(id);
  }

  @Patch(':id/reactivate')
  async reactivateUser(@Param('id', ParseIntPipe) id: number) {
    return this.adminUserService.reactivateUser(id);
  }

  // Additional administrative endpoints
  @Get('statistics/roles')
  async getUserRoleStatistics() {
    // Return count of users by role
    return {
      // Implementation in service
    };
  }

  @Get('statistics/activity')
  async getActivityStatistics() {
    // Return activity statistics
    return {
      // Implementation in service
    };
  }

  @Get('departments')
  async getDepartments() {
    // Return list of all departments
    return {
      // Implementation in service
    };
  }

  @Get('organizations')
  async getOrganizations() {
    // Return list of all organizations
    return {
      // Implementation in service
    };
  }

  @Post('import')
  async importUsers(@Body() usersData: any) {
    // Bulk import users
    return {
      // Implementation in service
    };
  }

  @Get('export')
  async exportUsers(@Query() filters: FilterUsersDto) {
    // Export users based on filters
    return {
      // Implementation in service
    };
  }

  @Post(':id/assign-organization')
  async assignOrganization(
    @Param('id', ParseIntPipe) id: number,
    @Body('organizationId', ParseIntPipe) organizationId: number,
  ) {
    // Assign user to organization
    return {
      // Implementation in service
    };
  }

  @Post(':id/assign-department')
  async assignDepartment(
    @Param('id', ParseIntPipe) id: number,
    @Body('department') department: string,
  ) {
    // Assign user to department
    return {
      // Implementation in service
    };
  }

  @Get('audit-log')
  @ApiOperation({ summary: 'Get user management audit log' })
  @ApiResponse({ status: 200, type: AuditLogResponse })
  @Roles(BaseRole.ADMIN, ReviewerRole.AUDITOR)
  async getAuditLog(@Query() filters: AuditLogFilterDto): Promise<AuditLogResponse> {
    return this.adminUserService.getAuditLog(filters);
  }

  @Post('notify')
  async notifyUsers(@Body() notification: any) {
    // Send notification to selected users
    return {
      // Implementation in service
    };
  }

  @Get('permissions')
  async getPermissions() {
    // Return list of all available permissions
    return {
      // Implementation in service
    };
  }

  @Post(':id/permissions')
  @ApiOperation({ summary: 'Update user permissions' })
  @ApiResponse({ status: 200, type: UserResponse })
  @Roles(BaseRole.ADMIN, ApproverRole.PERMISSION_MANAGER)
  async updateUserPermissions(
    @Param('id', ParseIntPipe) id: number,
    @Body() permissions: UpdatePermissionsDto,
  ): Promise<UserResponse> {
    return this.adminUserService.updateUserPermissions(id, permissions);
  }
}
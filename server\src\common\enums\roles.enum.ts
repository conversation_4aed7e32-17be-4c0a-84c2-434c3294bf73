export enum BaseRole {
  ADMIN = 'ADMIN',
  PARTNER = 'PARTNER',
  STAFF = 'STAFF'
}

export enum ReviewerRole {
  PARTNER_COORDINATOR = 'PARTNER_COORDINATOR',
  TECHNICAL_EXPERT = 'TECHNICAL_EXPERT',
  LEGAL_OFFICER = 'LEGAL_OFFICER',
  AUDITOR = 'AUDITOR'
}

export enum ApproverRole {
  HEAD_OF_DEPARTMENT = 'HEAD_OF_DEPARTMENT',
  LEGAL_OFFICER = 'LEGAL_OFFICER',
  PERMANENT_SECRETARY = 'PERMANENT_SECRETARY',
  MINISTER = 'MINISTER',
  ROLE_MANAGER = 'ROLE_MANAGER',
  PERMISSION_MANAGER = 'PERMISSION_MANAGER'
}

export enum PermissionLevel {
  NONE = 'NONE',
  READ = 'READ',
  WRITE = 'WRITE',
  ADMIN = 'ADMIN'
}

export enum ResourceType {
  USER = 'USER',
  ORGANIZATION = 'ORGANIZATION',
  APPLICATION = 'APPLICATION',
  DOCUMENT = 'DOCUMENT',
  REVIEW = 'REVIEW',
  APPROVAL = 'APPROVAL',
  REPORT = 'REPORT',
  AUDIT_LOG = 'AUDIT_LOG'
}

export interface RolePermission {
  resource: ResourceType;
  level: PermissionLevel;
}

export const DEFAULT_ROLE_PERMISSIONS: Record<BaseRole | ReviewerRole | ApproverRole, RolePermission[]> = {
  // Base Roles
  [BaseRole.ADMIN]: [
    { resource: ResourceType.USER, level: PermissionLevel.ADMIN },
    { resource: ResourceType.ORGANIZATION, level: PermissionLevel.ADMIN },
    { resource: ResourceType.APPLICATION, level: PermissionLevel.ADMIN },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.ADMIN },
    { resource: ResourceType.REVIEW, level: PermissionLevel.ADMIN },
    { resource: ResourceType.APPROVAL, level: PermissionLevel.ADMIN },
    { resource: ResourceType.REPORT, level: PermissionLevel.ADMIN },
    { resource: ResourceType.AUDIT_LOG, level: PermissionLevel.ADMIN }
  ],
  [BaseRole.PARTNER]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE },
    { resource: ResourceType.ORGANIZATION, level: PermissionLevel.READ }
  ],
  [BaseRole.STAFF]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.READ }
  ],

  // Reviewer Roles
  [ReviewerRole.PARTNER_COORDINATOR]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
    { resource: ResourceType.REVIEW, level: PermissionLevel.WRITE },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE }
  ],
  [ReviewerRole.TECHNICAL_EXPERT]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
    { resource: ResourceType.REVIEW, level: PermissionLevel.WRITE },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.READ }
  ],
  [ReviewerRole.LEGAL_OFFICER]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
    { resource: ResourceType.REVIEW, level: PermissionLevel.WRITE },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE }
  ],
  [ReviewerRole.AUDITOR]: [
    { resource: ResourceType.AUDIT_LOG, level: PermissionLevel.READ },
    { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
    { resource: ResourceType.REVIEW, level: PermissionLevel.READ }
  ],

  // Approver Roles
  [ApproverRole.HEAD_OF_DEPARTMENT]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
    { resource: ResourceType.APPROVAL, level: PermissionLevel.WRITE },
    { resource: ResourceType.REVIEW, level: PermissionLevel.READ }
  ],
  [ApproverRole.LEGAL_OFFICER]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
    { resource: ResourceType.APPROVAL, level: PermissionLevel.WRITE },
    { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE }
  ],
  [ApproverRole.PERMANENT_SECRETARY]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.ADMIN },
    { resource: ResourceType.APPROVAL, level: PermissionLevel.ADMIN },
    { resource: ResourceType.ORGANIZATION, level: PermissionLevel.WRITE }
  ],
  [ApproverRole.MINISTER]: [
    { resource: ResourceType.APPLICATION, level: PermissionLevel.ADMIN },
    { resource: ResourceType.APPROVAL, level: PermissionLevel.ADMIN },
    { resource: ResourceType.ORGANIZATION, level: PermissionLevel.ADMIN }
  ],
  [ApproverRole.ROLE_MANAGER]: [
    { resource: ResourceType.USER, level: PermissionLevel.WRITE },
    { resource: ResourceType.ORGANIZATION, level: PermissionLevel.WRITE }
  ],
  [ApproverRole.PERMISSION_MANAGER]: [
    { resource: ResourceType.USER, level: PermissionLevel.ADMIN },
    { resource: ResourceType.ORGANIZATION, level: PermissionLevel.ADMIN }
  ]
};

export function hasPermission(
  userRoles: (BaseRole | ReviewerRole | ApproverRole)[],
  resource: ResourceType,
  requiredLevel: PermissionLevel
): boolean {
  const userPermissions = userRoles.flatMap(role => DEFAULT_ROLE_PERMISSIONS[role] || []);
  const highestPermission = userPermissions
    .filter(permission => permission.resource === resource)
    .reduce((highest, current) => {
      const levels = Object.values(PermissionLevel);
      const highestLevel = levels.indexOf(highest?.level || PermissionLevel.NONE);
      const currentLevel = levels.indexOf(current.level);
      return currentLevel > highestLevel ? current : highest;
    }, null);

  if (!highestPermission) {
    return false;
  }

  const levels = Object.values(PermissionLevel);
  return levels.indexOf(highestPermission.level) >= levels.indexOf(requiredLevel);
}
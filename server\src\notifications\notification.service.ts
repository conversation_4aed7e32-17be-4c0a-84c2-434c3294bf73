import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../email/email.service';
import { UserRole } from '@prisma/client';

@Injectable()
export class NotificationService {
  constructor(
    private prisma: PrismaService,
    private emailService: EmailService,
  ) {}

  async notifyApplicationSubmitted(applicationId: number) {
    const application = await this.getApplicationWithDetails(applicationId);

    // Notify all Partner Coordinators
    const coordinators = await this.prisma.user.findMany({
      where: {
        role: UserRole.PARTNER_COORDINATOR,
        isActive: true,
      },
    });

    for (const coordinator of coordinators) {
      await this.createNotification({
        userId: coordinator.id,
        title: 'New Application Submitted',
        message: `A new MoU application (${application.applicationKey}) has been submitted and requires your review.`,
        type: 'APPLICATION_SUBMITTED',
        applicationId,
      });

      await this.emailService.sendWelcomeEmail({
        email: coordinator.email,
        firstName: coordinator.firstName,
        lastName: coordinator.lastName,
      });
    }
  }

  async notifyTechnicalExpertAssigned(applicationId: number, expertId: number) {
    const application = await this.getApplicationWithDetails(applicationId);
    const expert = await this.prisma.user.findUnique({ where: { id: expertId } });

    await this.createNotification({
      userId: expertId,
      title: 'New Technical Review Assignment',
      message: `You have been assigned to review MoU application ${application.applicationKey}.`,
      type: 'TECHNICAL_REVIEW_ASSIGNED',
      applicationId,
    });

    await this.emailService.sendWelcomeEmail({
      email: expert.email,
      firstName: expert.firstName,
      lastName: expert.lastName,
    });
  }

  async notifyReviewCompleted(applicationId: number, reviewerId: number, reviewType: string) {
    const application = await this.getApplicationWithDetails(applicationId);
    const reviewer = await this.prisma.user.findUnique({ where: { id: reviewerId } });

    // Notify Partner Coordinators
    const coordinators = await this.prisma.user.findMany({
      where: {
        role: UserRole.PARTNER_COORDINATOR,
        isActive: true,
      },
    });

    for (const coordinator of coordinators) {
      await this.createNotification({
        userId: coordinator.id,
        title: `${reviewType} Review Completed`,
        message: `A ${reviewType.toLowerCase()} review has been completed for application ${application.applicationKey}.`,
        type: 'REVIEW_COMPLETED',
        applicationId,
      });

      await this.emailService.sendMail({
        email: coordinator.email,
        firstName: coordinator.firstName,
        lastName: coordinator.lastName,
      });
    }
  }

  async notifyModificationRequested(applicationId: number, requesterId: number) {
    const application = await this.getApplicationWithDetails(applicationId);
    const requester = await this.prisma.user.findUnique({ where: { id: requesterId } });

    // Notify Partner Coordinators
    const coordinators = await this.prisma.user.findMany({
      where: {
        role: UserRole.PARTNER_COORDINATOR,
        isActive: true,
      },
    });

    for (const coordinator of coordinators) {
      await this.createNotification({
        userId: coordinator.id,
        title: 'Modification Requested',
        message: `Modifications have been requested for application ${application.applicationKey}.`,
        type: 'MODIFICATION_REQUESTED',
        applicationId,
      });

      await this.emailService.sendMail({
        email: coordinator.email,
        firstName: coordinator.firstName,
        lastName: coordinator.lastName,
      });
    }
  }

  async notifyApprovalRequired(applicationId: number, approverRole: UserRole) {
    const application = await this.getApplicationWithDetails(applicationId);

    // Find relevant approvers
    const approvers = await this.prisma.user.findMany({
      where: {
        role: approverRole,
        isActive: true,
      },
    });

    for (const approver of approvers) {
      await this.createNotification({
        userId: approver.id,
        title: 'Approval Required',
        message: `Your approval is required for MoU application ${application.applicationKey}.`,
        type: 'APPROVAL_REQUIRED',
        applicationId,
      });

      await this.emailService.sendMail({
        email: approver.email,
        firstName: approver.firstName,
        lastName: approver.lastName,
      });
    }
  }

  async notifyApplicationApproved(applicationId: number) {
    const application = await this.getApplicationWithDetails(applicationId);

    // Notify partner - get organization from application
    const applicationWithOrg = await this.prisma.mouApplication.findUnique({
      where: { id: applicationId },
      include: {
        user: {
          include: {
            organization: true
          }
        }
      }
    });

    const partner = applicationWithOrg?.user;

    if (partner) {
      await this.createNotification({
        userId: partner.id,
        title: 'Application Approved',
        message: `Your MoU application ${application.applicationKey} has been approved.`,
        type: 'APPLICATION_APPROVED',
        applicationId,
      });

      await this.emailService.sendMail({
        email: partner.email,
        firstName: partner.firstName,
        lastName: partner.lastName,
      });
    }
  }

  async createNotification(data: {
    userId: number;
    title: string;
    message: string;
    type: string;
    applicationId: number;
  }) {
    // Since there's no Notification model, we'll just log for now
    console.log('Notification:', data);
    return { id: Date.now(), ...data };
  }

  private async getApplicationWithDetails(applicationId: number) {
    return this.prisma.mouApplication.findUnique({
      where: { id: applicationId },
      include: {
        approvalSteps: true,
        projects: true,
      },
    });
  }

  async markNotificationAsRead(notificationId: number, userId: number) {
    // Since there's no Notification model, we'll just log for now
    console.log('Mark notification as read:', { notificationId, userId });
    return { id: notificationId, isRead: true, readAt: new Date() };
  }

  async getUserNotifications(userId: number, options: {
    skip?: number;
    take?: number;
    includeRead?: boolean;
  } = {}) {
    // Since there's no Notification model, we'll return empty array
    console.log('Get user notifications:', { userId, options });
    return [];
  }

  async getUnreadCount(userId: number) {
    // Since there's no Notification model, we'll return 0
    console.log('Get unread count for user:', userId);
    return 0;
  }
}
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../email/email.service';
import { ApplicationStatus, UserRole } from '@prisma/client';

@Injectable()
export class NotificationService {
  constructor(
    private prisma: PrismaService,
    private emailService: EmailService,
  ) {}

  async notifyApplicationSubmitted(applicationId: number) {
    const application = await this.getApplicationWithDetails(applicationId);

    // Notify all Partner Coordinators
    const coordinators = await this.prisma.user.findMany({
      where: {
        role: UserRole.PARTNER_COORDINATOR,
        isActive: true,
      },
    });

    for (const coordinator of coordinators) {
      await this.createNotification({
        userId: coordinator.id,
        title: 'New Application Submitted',
        message: `A new MoU application (${application.reference}) has been submitted and requires your review.`,
        type: 'APPLICATION_SUBMITTED',
        applicationId,
      });

      await this.emailService.sendMail({
        to: coordinator.email,
        subject: 'New MoU Application Submitted',
        template: 'application-submitted',
        context: {
          coordinatorName: `${coordinator.firstName} ${coordinator.lastName}`,
          applicationReference: application.reference,
          applicationTitle: application.title,
        },
      });
    }
  }

  async notifyTechnicalExpertAssigned(applicationId: number, expertId: number) {
    const application = await this.getApplicationWithDetails(applicationId);
    const expert = await this.prisma.user.findUnique({ where: { id: expertId } });

    await this.createNotification({
      userId: expertId,
      title: 'New Technical Review Assignment',
      message: `You have been assigned to review MoU application ${application.reference}.`,
      type: 'TECHNICAL_REVIEW_ASSIGNED',
      applicationId,
    });

    await this.emailService.sendMail({
      to: expert.email,
      subject: 'New Technical Review Assignment',
      template: 'technical-review-assigned',
      context: {
        expertName: `${expert.firstName} ${expert.lastName}`,
        applicationReference: application.reference,
        applicationTitle: application.title,
      },
    });
  }

  async notifyReviewCompleted(applicationId: number, reviewerId: number, reviewType: string) {
    const application = await this.getApplicationWithDetails(applicationId);
    const reviewer = await this.prisma.user.findUnique({ where: { id: reviewerId } });

    // Notify Partner Coordinators
    const coordinators = await this.prisma.user.findMany({
      where: {
        role: UserRole.PARTNER_COORDINATOR,
        isActive: true,
      },
    });

    for (const coordinator of coordinators) {
      await this.createNotification({
        userId: coordinator.id,
        title: `${reviewType} Review Completed`,
        message: `A ${reviewType.toLowerCase()} review has been completed for application ${application.reference}.`,
        type: 'REVIEW_COMPLETED',
        applicationId,
      });

      await this.emailService.sendMail({
        to: coordinator.email,
        subject: `${reviewType} Review Completed`,
        template: 'review-completed',
        context: {
          coordinatorName: `${coordinator.firstName} ${coordinator.lastName}`,
          reviewType,
          reviewerName: `${reviewer.firstName} ${reviewer.lastName}`,
          applicationReference: application.reference,
          applicationTitle: application.title,
        },
      });
    }
  }

  async notifyModificationRequested(applicationId: number, requesterId: number) {
    const application = await this.getApplicationWithDetails(applicationId);
    const requester = await this.prisma.user.findUnique({ where: { id: requesterId } });

    // Notify Partner Coordinators
    const coordinators = await this.prisma.user.findMany({
      where: {
        role: UserRole.PARTNER_COORDINATOR,
        isActive: true,
      },
    });

    for (const coordinator of coordinators) {
      await this.createNotification({
        userId: coordinator.id,
        title: 'Modification Requested',
        message: `Modifications have been requested for application ${application.reference}.`,
        type: 'MODIFICATION_REQUESTED',
        applicationId,
      });

      await this.emailService.sendMail({
        to: coordinator.email,
        subject: 'Application Modification Requested',
        template: 'modification-requested',
        context: {
          coordinatorName: `${coordinator.firstName} ${coordinator.lastName}`,
          requesterName: `${requester.firstName} ${requester.lastName}`,
          requesterRole: requester.role,
          applicationReference: application.reference,
          applicationTitle: application.title,
        },
      });
    }
  }

  async notifyApprovalRequired(applicationId: number, approverRole: ApproverRole) {
    const application = await this.getApplicationWithDetails(applicationId);

    // Find relevant approvers
    const approvers = await this.prisma.user.findMany({
      where: {
        approverRole,
        isActive: true,
      },
    });

    for (const approver of approvers) {
      await this.createNotification({
        userId: approver.id,
        title: 'Approval Required',
        message: `Your approval is required for MoU application ${application.reference}.`,
        type: 'APPROVAL_REQUIRED',
        applicationId,
      });

      await this.emailService.sendMail({
        to: approver.email,
        subject: 'MoU Application Approval Required',
        template: 'approval-required',
        context: {
          approverName: `${approver.firstName} ${approver.lastName}`,
          applicationReference: application.reference,
          applicationTitle: application.title,
        },
      });
    }
  }

  async notifyApplicationApproved(applicationId: number) {
    const application = await this.getApplicationWithDetails(applicationId);

    // Notify partner
    const partner = await this.prisma.user.findFirst({
      where: {
        organizationId: application.partnerId,
        role: UserRole.PARTNER,
      },
    });

    if (partner) {
      await this.createNotification({
        userId: partner.id,
        title: 'Application Approved',
        message: `Your MoU application ${application.reference} has been approved.`,
        type: 'APPLICATION_APPROVED',
        applicationId,
      });

      await this.emailService.sendMail({
        to: partner.email,
        subject: 'MoU Application Approved',
        template: 'application-approved',
        context: {
          partnerName: `${partner.firstName} ${partner.lastName}`,
          applicationReference: application.reference,
          applicationTitle: application.title,
        },
      });
    }
  }

  async createNotification(data: {
    userId: number;
    title: string;
    message: string;
    type: string;
    applicationId: number;
  }) {
    return this.prisma.notification.create({
      data: {
        userId: data.userId,
        title: data.title,
        message: data.message,
        type: data.type,
        applicationId: data.applicationId,
        isRead: false,
      },
    });
  }

  private async getApplicationWithDetails(applicationId: number) {
    return this.prisma.mouApplication.findUnique({
      where: { id: applicationId },
      include: {
        approvalSteps: true,
        projects: true,
      },
    });
  }

  async markNotificationAsRead(notificationId: number, userId: number) {
    return this.prisma.notification.update({
      where: {
        id: notificationId,
        userId, // Ensure the notification belongs to the user
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
  }

  async getUserNotifications(userId: number, options: {
    skip?: number;
    take?: number;
    includeRead?: boolean;
  } = {}) {
    const { skip = 0, take = 10, includeRead = false } = options;

    return this.prisma.notification.findMany({
      where: {
        userId,
        ...(includeRead ? {} : { isRead: false }),
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take,
    });
  }

  async getUnreadCount(userId: number) {
    return this.prisma.notification.count({
      where: {
        userId,
        isRead: false,
      },
    });
  }
}
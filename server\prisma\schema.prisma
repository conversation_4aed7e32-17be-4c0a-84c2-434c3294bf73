// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  PARTNER
  PARTNER_COORDINATOR
  TECHNICAL_EXPERT
  LEGAL_OFFICER
  HEAD_OF_DEPARTMENT
  PermanentSecretary
  MINISTER
}
enum ApprovalStatusType {
  PENDING
  APPROVED
  REJECTED
  MODIFICATIONS_REQUESTED
}

enum AddressType {
  HEADQUARTERS
  RWANDA
}

enum ApplicationStatus {
  DRAFT
  SUBMITTED
  IN_REVIEW
  IN_TECHNICAL_REVIEW
  IN_LEGAL_REVIEW
  IN_HOD_REVIEW
  IN_PS_REVIEW
  IN_MINISTER_REVIEW
  MODIFICATIONS_REQUESTED
  APPROVED
  REJECTED
}

enum ModificationSource {
  PARTNER_COORDINATOR
  LEGAL_OFFICER
  TECHNICAL_EXPERT
  HEAD_OF_DEPARTMENT
  PERMANENT_SECRETARY
  MINISTER
}

// Base models (no dependencies)
model Currency {
  id           Int    @id @default(autoincrement())
  currencyCode String @unique @map("currency_code")
  currencyName String @map("currency_name")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model DocumentType {
  id       Int    @id @default(autoincrement())
  typeName String @map("type_name")

  // Relations
  documents Document[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model BudgetType {
  id       Int    @id @default(autoincrement())
  typeName String @map("type_name")

  // Relations
  projects Project[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model FundingUnit {
  id       Int    @id @default(autoincrement())
  unitName String @map("unit_name")

  // Relations
  projects Project[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model FundingSource {
  id         Int    @id @default(autoincrement())
  sourceName String @map("source_name")

  // Relations
  projects Project[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model FinancingAgent {
  id          Int     @id @default(autoincrement())
  agentName   String  @unique @map("agent_name")
  description String?
  contactInfo String? @map("contact_info")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model FinancingScheme {
  id          Int     @id @default(autoincrement())
  schemeName  String  @unique @map("scheme_name")
  description String?
  terms       String?
  conditions  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model HealthCareProvider {
  id           Int     @id @default(autoincrement())
  providerName String  @unique @map("provider_name")
  description  String?
  location     String?
  contactEmail String? @map("contact_email")
  contactPhone String? @map("contact_phone")
  website      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// Models with self-relations
model InputCategory {
  id           Int     @id @default(autoincrement())
  categoryName String  @unique @map("category_name")
  description  String?
  parentId     Int?    @map("parent_id")

  // Self relation
  parent   InputCategory?  @relation("InputCategoryHierarchy", fields: [parentId], references: [id])
  children InputCategory[] @relation("InputCategoryHierarchy")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model DomainIntervention {
  id          Int     @id @default(autoincrement())
  domainName  String  @map("domain_name")
  description String?
  parentId    Int?    @map("parent_id")

  // Self relation
  parent   DomainIntervention?  @relation("DomainHierarchy", fields: [parentId], references: [id])
  children DomainIntervention[] @relation("DomainHierarchy")

  // Relations
  activities Activity[]
  subDomainInterventions SubDomainIntervention[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
  User      User?    @relation(fields: [userId], references: [id])
  userId    Int?
}

// Organization models
model OrganizationType {
  id       Int    @id @default(autoincrement())
  typeName String @unique @map("type_name")

  // Relations
  organizations Organization[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model CentralLevelInstitution {
  id          Int @id @default(autoincrement())
  name        String @unique
  isActive    Boolean @default(true)  // Added to mark institutions as active or inactive
  organizations Organization[]
  activities ActivityCentralLevelInstitution[]  // Ensure this is the only activities field

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Organization {
  id                             Int     @id @default(autoincrement())
  organizationName               String  @map("organization_name")
  organizationPhoneNumber        String  @map("organization_phone_number")
  organizationEmail              String  @unique @map("organization_email")
  organizationWebsite            String? @map("organization_website")
  homeCountryRepresentative      String  @map("home_country_representative")
  rwandaRepresentative           String  @map("rwanda_representative")
  organizationRgbNumber          String  @map("organization_rgb_number")
  organizationTypeId             Int     @map("organization_type_id")
  centralLevelInstitutionId      Int?    @map("central_level_institution_id")

  // Relations
  organizationType OrganizationType @relation(fields: [organizationTypeId], references: [id])
  parties          Party[]
  documents        Document[]
  projects         Project[]
  users            User[]
  draftApplications DraftMouApplication[]
  addresses Address[]
  centralLevelInstitution CentralLevelInstitution? @relation(fields: [centralLevelInstitutionId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Address {
  id          Int         @id @default(autoincrement())
  addressType AddressType @map("address_type")
  country     String
  province    String?
  district    String?
  street      String?
  poBox       String?      @map("po_box")
  postalCode  String?     @map("postal_code")

  // Relations
  organizationId Int          @map("organization_id")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@unique([organizationId, addressType])
}

// User model
model User {
  id            Int       @id @default(autoincrement())
  firstName     String    @map("first_name")
  lastName      String    @map("last_name")
  email         String    @unique
  password      String
  emailVerified Boolean   @default(false) @map("email_verified")
  verifiedAt    DateTime? @map("verified_at")

  verificationToken           String?   @map("verification_token")
  verificationTokenExpiryTime DateTime? @map("verification_token_expiry_time")
  refreshToken                String?   @map("refresh_token")

  passwordResetToken   String?   @map("password_reset_token")
  passwordResetExpires DateTime? @map("password_reset_expires")
  invitationToken      String?   @map("invitation_token")
  invitationExpires    DateTime? @map("invitation_expires")
  invitedBy            String?   @map("invited_by")

  role      UserRole @default(PARTNER)
  isActive  Boolean  @default(true) @map("is_active")

  // Relations
  organizationId     Int?                 @map("organization_id")
  organization       Organization?        @relation(fields: [organizationId], references: [id])
  assignedDomains    DomainIntervention[]
  mouApplications    MouApplication[]
  approvalStepsGiven ApprovalStep[]       @relation("ApprovalReviewer")
  draftApplications  DraftMouApplication[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// Project models
model Project {
  id          Int     @id @default(autoincrement())
  name        String
  description String?
  duration    Int     @map("duration")  // Duration in years
  budgetTypeId      Int @map("budget_type_id")
  fundingUnitId     Int @map("funding_unit_id")
  fundingSourceId   Int @map("funding_source_id")
  organizationId    Int @map("organization_id")
  mouApplicationId  Int @map("mou_application_id")
  
  // Relations
  budgetType      BudgetType     @relation(fields: [budgetTypeId], references: [id])
  fundingUnit     FundingUnit    @relation(fields: [fundingUnitId], references: [id])
  fundingSource   FundingSource  @relation(fields: [fundingSourceId], references: [id])
  organization    Organization   @relation(fields: [organizationId], references: [id])
  mouApplication  MouApplication @relation(fields: [mouApplicationId], references: [id])
  activities      Activity[]
  documents       Document[]  // Updated to one-to-many for supporting documents
  approvalSteps   ApprovalStep[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Document {
  id             Int     @id @default(autoincrement())
  name           String
  description    String?
  organizationId Int     @map("organization_id")
  documentTypeId Int     @map("document_type_id")
  projectId      Int?    @map("project_id")  // Added for bidirectional relation
  
  // Relations
  organization Organization @relation(fields: [organizationId], references: [id])
  documentType DocumentType @relation(fields: [documentTypeId], references: [id])
  project      Project?     @relation(fields: [projectId], references: [id])  // Links back to Project
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Province {
  id         Int @id @default(autoincrement())
  name       String @unique  // e.g., 'Kigali City'
  activities ActivityProvince[]  // For many-to-many junction

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model ActivityProvince {
  id          Int @id @default(autoincrement())
  activityId  Int
  provinceId  Int
  activity    Activity @relation(fields: [activityId], references: [id])
  province    Province @relation(fields: [provinceId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([activityId, provinceId])
}

model ActivityCentralLevelInstitution {
  id                          Int  @id @default(autoincrement())
  activityId                  Int
  centralLevelInstitutionId   Int
  activity                    Activity @relation(fields: [activityId], references: [id])
  centralLevelInstitution     CentralLevelInstitution @relation(fields: [centralLevelInstitutionId], references: [id])

  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt

  @@unique([activityId, centralLevelInstitutionId])
}

model Activity {
  id                   Int @id @default(autoincrement())
  name                 String
  description          String?
  projectId            Int      @map("project_id")
  startDate            DateTime @map("start_date")
  endDate              DateTime @map("end_date")
  implementer          String
  implementerUnit      String   @map("implementer_unit")
  fiscalYear           Int      @map("fiscal_year")
  domainInterventionId Int      @map("domain_intervention_id")
  inputId              Int      @map("input_id") // Keeping for now, but can be removed if not needed
  provinces            ActivityProvince[]  // Many-to-many relation
  centralLevelInstitutions ActivityCentralLevelInstitution[]  // Ensure this is present
  
  // Relations
  project                  Project                  @relation(fields: [projectId], references: [id])
  domainIntervention       DomainIntervention       @relation(fields: [domainInterventionId], references: [id])
  input                    Input                    @relation(fields: [inputId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// Input models
model Input {
  id              Int    @id @default(autoincrement())
  name            String
  activities    Activity[]
  inputSubclass InputSubclass[] 
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model InputSubclass {
  id         Int    @id @default(autoincrement())
  subclassId Int    @unique @map("subclass_id")
  name       String
  budget     Float
  inputId    Int    @map("input_id")

  // Relations
  input      Input? @relation(fields: [inputId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

// MoU models
model Party {
  id                        Int     @id @default(autoincrement())
  partyId                   String  @unique @map("party_id")
  name                      String
  responsibilityId          Int?    @map("responsibility_id")
  organizationId            Int     @map("organization_id")
  objectiveId               Int?    @map("objective_id")
  goalId                    Int     @map("goal_id")
  signatory                 String
  position                  String
  duration                  Int     @default(1)
  reasonForExtendedDuration String? @map("reason_for_extended_duration")

  // Relations
  organization    Organization    @relation(fields: [organizationId], references: [id])
  responsibility  Responsibility? @relation(fields: [responsibilityId], references: [id])
  objective       Objective?      @relation(fields: [objectiveId], references: [id])
  goal            Goal            @relation(fields: [goalId], references: [id])
  mou             Mou?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Responsibility {
  id   Int    @id @default(autoincrement())
  name String

  // Relations
  parties Party[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Objective {
  id   Int    @id @default(autoincrement())
  name String

  // Relations
  parties Party[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Goal {
  id   Int    @id @default(autoincrement())
  name String

  // Relations
  parties Party[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model Mou {
  id      Int    @id @default(autoincrement())
  mouKey  String @unique @map("mou_key")
  partyId Int    @unique @map("party_id")

  // Relations
  party           Party            @relation(fields: [partyId], references: [id])
  mouApplications MouApplication[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model MouApplication {
  id             Int     @id @default(autoincrement())
  applicationKey String  @unique @map("application_key")
  mouId          Int     @map("mou_id")
  userId         Int?    @map("user_id")

  // Relations
  mou           Mou            @relation(fields: [mouId], references: [id])
  user          User?          @relation(fields: [userId], references: [id])
  projects      Project[]
  approvalSteps ApprovalStep[]
  activityLogs  ActivityLog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)
}

model ApprovalStep {
  id               Int                @id @default(autoincrement())
  mouApplicationId Int                @map("mou_application_id")
  reviewerId       Int                @map("reviewer_id")
  projectId        Int?               @map("project_id")
  status           ApprovalStatusType @default(PENDING)
  comment          String?
  role             UserRole

  // Relations
  mouApplication MouApplication @relation(fields: [mouApplicationId], references: [id])
  reviewer       User           @relation("ApprovalReviewer", fields: [reviewerId], references: [id])
  project        Project?       @relation(fields: [projectId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@map("approval_steps")
}

// Draft MoU Application model for saving work in progress
model DraftMouApplication {
  id             String  @id @default(cuid())
  userId         Int     @map("user_id")
  organizationId Int?    @map("organization_id")
  currentStep    Int     @default(1) @map("current_step")

  // MoU Details (JSON)
  mouDetails     Json?   @map("mou_details")

  // Parties (JSON array)
  parties        Json?

  // Projects (JSON array)
  projects       Json?

  // Activities (JSON array)
  activities     Json?

  // Documents (JSON array)
  documents      Json?

  // Relations
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization? @relation(fields: [organizationId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  deleted   Boolean  @default(false)

  @@unique([userId]) // One draft per user
  @@map("draft_mou_applications")
}

model ActivityLog {
  id        Int      @id @default(autoincrement())
  action    String
  details   Json
  category  String
  importance String
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")

  // Relations
  mouApplicationId Int?     @map("mou_application_id")
  mouApplication   MouApplication? @relation(fields: [mouApplicationId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  deleted   Boolean  @default(false)

  @@map("activity_logs")
}

model DomainFunction {
  id                Int @id @default(autoincrement())
  name              String
  description       String?
  subDomainInterventionId Int @map("sub_domain_intervention_id")
  // Relations
  subDomainIntervention SubDomainIntervention @relation(fields: [subDomainInterventionId], references: [id])
  subDomainFunctions SubDomainFunction[]

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  deleted           Boolean @default(false)
}

model SubDomainFunction {
  id                Int @id @default(autoincrement())
  name              String
  description       String?
  domainFunctionId  Int @map("domain_function_id")

  // Relations
  domainFunction    DomainFunction @relation(fields: [domainFunctionId], references: [id])

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  deleted           Boolean @default(false)
}

model SubDomainIntervention {
  id                Int @id @default(autoincrement())
  name              String
  description       String?
  domainInterventionId Int @map("domain_intervention_id")

  // Relations
  domainIntervention DomainIntervention @relation(fields: [domainInterventionId], references: [id])
  domainFunctions   DomainFunction[]

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  deleted           Boolean @default(false)
}
import { AdminDashboardService } from './admin-dashboard.service';
export declare class AdminDashboardController {
    private readonly dashboardService;
    constructor(dashboardService: AdminDashboardService);
    getDashboardOverview(): Promise<{
        userStats: {
            totalUsers: any;
            activeUsers: any;
            inactiveUsers: any;
            recentlyActive: any;
            roleDistribution: any;
            departmentDistribution: any;
        };
        activityMetrics: {
            loginActivity: any;
            userActions: any;
            reviewActivity: any;
            approvalActivity: any;
        };
        systemHealth: {
            activeApplications: any;
            pendingReviews: any;
            pendingApprovals: any;
            recentErrors: any;
            systemStatus: {
                status: string;
                lastCheck: Date;
                components: {
                    database: string;
                    email: string;
                    storage: string;
                    notifications: string;
                };
            };
        };
        performanceMetrics: {
            averageReviewTime: number;
            averageApprovalTime: number;
            applicationThroughput: any;
            userProductivity: any;
        };
    }>;
    getUserStatistics(): Promise<{
        totalUsers: any;
        activeUsers: any;
        inactiveUsers: any;
        recentlyActive: any;
        roleDistribution: any;
        departmentDistribution: any;
    }>;
    getActivityMetrics(startDate?: Date, endDate?: Date): Promise<{
        loginActivity: any;
        userActions: any;
        reviewActivity: any;
        approvalActivity: any;
    }>;
    getSystemHealth(): Promise<{
        activeApplications: any;
        pendingReviews: any;
        pendingApprovals: any;
        recentErrors: any;
        systemStatus: {
            status: string;
            lastCheck: Date;
            components: {
                database: string;
                email: string;
                storage: string;
                notifications: string;
            };
        };
    }>;
    getPerformanceMetrics(): Promise<{
        averageReviewTime: number;
        averageApprovalTime: number;
        applicationThroughput: any;
        userProductivity: any;
    }>;
    getReviewStatistics(startDate?: Date, endDate?: Date): Promise<any>;
    getApprovalStatistics(startDate?: Date, endDate?: Date): Promise<any>;
    getActiveUsers(period?: 'daily' | 'weekly' | 'monthly', startDate?: Date, endDate?: Date): Promise<any>;
    getApplicationThroughput(period?: 'daily' | 'weekly' | 'monthly', startDate?: Date, endDate?: Date): Promise<any>;
    getUserProductivity(startDate?: Date, endDate?: Date, department?: string, role?: string): Promise<any>;
    getSystemErrors(startDate?: Date, endDate?: Date, severity?: string): Promise<any>;
    getDepartmentPerformance(startDate?: Date, endDate?: Date): Promise<{
        reviewTimes: number;
        approvalTimes: number;
        throughput: any;
    }>;
    getRoleActivity(startDate?: Date, endDate?: Date): Promise<{
        distribution: any;
        activity: any;
    }>;
    getNotificationStatistics(startDate?: Date, endDate?: Date): Promise<{}>;
    getAuditSummary(startDate?: Date, endDate?: Date): Promise<{}>;
}

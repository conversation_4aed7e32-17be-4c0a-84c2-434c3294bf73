import { IsE<PERSON>, <PERSON>String, IsEnum, IsOptional, IsBoolean, IsN<PERSON>ber, IsArray } from 'class-validator';
import { UserRole, ReviewerRole, ApproverRole } from '@prisma/client';

export class CreateUserDto {
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsEnum(UserRole)
  baseRole: UserRole;

  @IsOptional()
  @IsEnum(ReviewerRole)
  reviewerRole?: ReviewerRole;

  @IsOptional()
  @IsEnum(ApproverRole)
  approverRole?: ApproverRole;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsNumber()
  organizationId?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsEnum(UserRole)
  baseRole?: UserRole;

  @IsOptional()
  @IsEnum(ReviewerRole)
  reviewerRole?: ReviewerRole;

  @IsOptional()
  @IsEnum(ApproverRole)
  approverRole?: ApproverRole;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsNumber()
  organizationId?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AssignRolesDto {
  @IsOptional()
  @IsEnum(UserRole)
  baseRole?: UserRole;

  @IsOptional()
  @IsEnum(ReviewerRole)
  reviewerRole?: ReviewerRole;

  @IsOptional()
  @IsEnum(ApproverRole)
  approverRole?: ApproverRole;
}

export class FilterUsersDto {
  @IsOptional()
  @IsArray()
  @IsEnum(UserRole, { each: true })
  baseRoles?: UserRole[];

  @IsOptional()
  @IsArray()
  @IsEnum(ReviewerRole, { each: true })
  reviewerRoles?: ReviewerRole[];

  @IsOptional()
  @IsArray()
  @IsEnum(ApproverRole, { each: true })
  approverRoles?: ApproverRole[];

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsNumber()
  organizationId?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsNumber()
  page?: number;

  @IsOptional()
  @IsNumber()
  limit?: number;

  @IsOptional()
  @IsString()
  sortBy?: string;

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

export class UserActivityDto {
  @IsNumber()
  userId: number;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsString()
  activityType?: string;

  @IsOptional()
  @IsNumber()
  page?: number;

  @IsOptional()
  @IsNumber()
  limit?: number;
}

export class BulkUserActionDto {
  @IsArray()
  @IsNumber({}, { each: true })
  userIds: number[];

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsEnum(UserRole)
  baseRole?: UserRole;

  @IsOptional()
  @IsEnum(ReviewerRole)
  reviewerRole?: ReviewerRole;

  @IsOptional()
  @IsEnum(ApproverRole)
  approverRole?: ApproverRole;
}

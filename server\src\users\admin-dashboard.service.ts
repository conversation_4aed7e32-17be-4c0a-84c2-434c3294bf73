import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AdminDashboardService {
  constructor(private prisma: PrismaService) {}

  async getUserStatistics() {
    const [
      totalUsers,
      activeUsers,
      inactiveUsers,
      recentlyActive,
      roleDistribution,
      departmentDistribution
    ] = await Promise.all([
      // Total users count
      this.prisma.user.count(),

      // Active users count
      this.prisma.user.count({
        where: { isActive: true }
      }),

      // Inactive users count
      this.prisma.user.count({
        where: { isActive: false }
      }),

      // Recently active users (last 30 days)
      this.prisma.user.count({
        where: {
          lastLoginAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      }),

      // Distribution by role
      this.getRoleDistribution(),

      // Distribution by department
      this.getDepartmentDistribution()
    ]);

    return {
      totalUsers,
      activeUsers,
      inactiveUsers,
      recentlyActive,
      roleDistribution,
      departmentDistribution
    };
  }

  async getActivityMetrics(startDate?: Date, endDate?: Date) {
    const dateFilter = this.getDateFilter(startDate, endDate);

    const [
      loginActivity,
      userActions,
      reviewActivity,
      approvalActivity
    ] = await Promise.all([
      // Login activity
      this.getLoginActivity(dateFilter),

      // User actions
      this.getUserActions(dateFilter),

      // Review activity
      this.getReviewActivity(dateFilter),

      // Approval activity
      this.getApprovalActivity(dateFilter)
    ]);

    return {
      loginActivity,
      userActions,
      reviewActivity,
      approvalActivity
    };
  }

  async getSystemHealth() {
    const [
      activeApplications,
      pendingReviews,
      pendingApprovals,
      recentErrors
    ] = await Promise.all([
      // Active applications count
      this.prisma.application.count({
        where: {
          status: {
            notIn: ['APPROVED', 'REJECTED', 'ARCHIVED']
          }
        }
      }),

      // Pending reviews count
      this.prisma.application.count({
        where: {
          status: 'IN_REVIEW'
        }
      }),

      // Pending approvals count
      this.prisma.application.count({
        where: {
          status: 'PENDING_APPROVAL'
        }
      }),

      // Recent system errors
      this.prisma.activityLog.findMany({
        where: {
          importance: 'ERROR',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      })
    ]);

    return {
      activeApplications,
      pendingReviews,
      pendingApprovals,
      recentErrors,
      systemStatus: this.getSystemStatus()
    };
  }

  async getPerformanceMetrics() {
    return {
      averageReviewTime: await this.calculateAverageReviewTime(),
      averageApprovalTime: await this.calculateAverageApprovalTime(),
      applicationThroughput: await this.calculateApplicationThroughput(),
      userProductivity: await this.calculateUserProductivity()
    };
  }

  private async getRoleDistribution() {
    const roles = await this.prisma.user.groupBy({
      by: ['baseRole'],
      _count: {
        baseRole: true
      }
    });

    const reviewerRoles = await this.prisma.user.groupBy({
      by: ['reviewerRole'],
      _count: {
        reviewerRole: true
      }
    });

    const approverRoles = await this.prisma.user.groupBy({
      by: ['approverRole'],
      _count: {
        approverRole: true
      }
    });

    return {
      baseRoles: roles,
      reviewerRoles,
      approverRoles
    };
  }

  private async getDepartmentDistribution() {
    return this.prisma.user.groupBy({
      by: ['department'],
      _count: {
        department: true
      }
    });
  }

  private async getLoginActivity(dateFilter: any) {
    return this.prisma.activityLog.groupBy({
      by: ['createdAt'],
      where: {
        action: 'LOGIN',
        ...dateFilter
      },
      _count: {
        action: true
      }
    });
  }

  private async getUserActions(dateFilter: any) {
    return this.prisma.activityLog.groupBy({
      by: ['action'],
      where: dateFilter,
      _count: {
        action: true
      }
    });
  }

  private async getReviewActivity(dateFilter: any) {
    return this.prisma.reviewDecision.groupBy({
      by: ['reviewerRole', 'decision'],
      where: {
        createdAt: dateFilter.createdAt
      },
      _count: {
        decision: true
      }
    });
  }

  private async getApprovalActivity(dateFilter: any) {
    return this.prisma.approvalDecision.groupBy({
      by: ['approverRole', 'decision'],
      where: {
        createdAt: dateFilter.createdAt
      },
      _count: {
        decision: true
      }
    });
  }

  private async calculateAverageReviewTime() {
    // Calculate average time between submission and review completion
    const reviews = await this.prisma.reviewDecision.findMany({
      include: {
        application: true
      }
    });

    let totalTime = 0;
    let count = 0;

    reviews.forEach(review => {
      const submissionDate = review.application.submittedAt;
      const reviewDate = review.createdAt;
      totalTime += reviewDate.getTime() - submissionDate.getTime();
      count++;
    });

    return count > 0 ? totalTime / count / (1000 * 60 * 60) : 0; // Return average in hours
  }

  private async calculateAverageApprovalTime() {
    // Similar to review time calculation but for approvals
    const approvals = await this.prisma.approvalDecision.findMany({
      include: {
        application: true
      }
    });

    let totalTime = 0;
    let count = 0;

    approvals.forEach(approval => {
      const submissionDate = approval.application.submittedAt;
      const approvalDate = approval.createdAt;
      totalTime += approvalDate.getTime() - submissionDate.getTime();
      count++;
    });

    return count > 0 ? totalTime / count / (1000 * 60 * 60) : 0;
  }

  private async calculateApplicationThroughput() {
    // Calculate applications processed per day over the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    return this.prisma.application.groupBy({
      by: ['status'],
      where: {
        updatedAt: {
          gte: thirtyDaysAgo
        }
      },
      _count: {
        status: true
      }
    });
  }

  private async calculateUserProductivity() {
    // Calculate actions per user over the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    return this.prisma.activityLog.groupBy({
      by: ['userId'],
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      _count: {
        id: true
      }
    });
  }

  private getDateFilter(startDate?: Date, endDate?: Date) {
    const filter: any = {};

    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.gte = startDate;
      if (endDate) filter.createdAt.lte = endDate;
    }

    return filter;
  }

  private getSystemStatus() {
    // You can implement actual system health checks here
    return {
      status: 'HEALTHY',
      lastCheck: new Date(),
      components: {
        database: 'OPERATIONAL',
        email: 'OPERATIONAL',
        storage: 'OPERATIONAL',
        notifications: 'OPERATIONAL'
      }
    };
  }
}

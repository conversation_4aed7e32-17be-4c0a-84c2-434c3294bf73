import { PrismaService } from '../prisma/prisma.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
export declare class AddressesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findByOrganization(organizationId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }[]>;
    findOne(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }>;
    create(createAddressDto: CreateAddressDto, currentUserId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }>;
    update(id: string, updateAddressDto: UpdateAddressDto, currentUserId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }>;
    remove(id: string, currentUserId: string): Promise<{
        message: string;
    }>;
}

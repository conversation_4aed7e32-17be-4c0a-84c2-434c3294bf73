import { AddressesService } from './addresses.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
export declare class AddressesController {
    private readonly addressesService;
    constructor(addressesService: AddressesService);
    findByOrganization(organizationId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }[]>;
    findOne(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }>;
    create(createAddressDto: CreateAddressDto, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }>;
    update(id: string, updateAddressDto: UpdateAddressDto, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationId: number;
        province: string | null;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        district: string | null;
        street: string | null;
        poBox: string | null;
        postalCode: string | null;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}

import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from './dto';
export declare class OrganizationsController {
    private readonly organizationsService;
    constructor(organizationsService: OrganizationsService);
    findAll(req: any): Promise<({
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    })[]>;
    findOne(id: string, req: any): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    }>;
    create(createOrganizationDto: CreateOrganizationDto, req: any): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    }>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, req: any): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}

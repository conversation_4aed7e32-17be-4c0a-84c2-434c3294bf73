import { Injectable, CanActivate, ExecutionContext, SetMetadata, applyDecorators } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '@prisma/client';
import { ResourceType, PermissionLevel, hasPermission } from '../../common/enums/roles.enum';

export const ROLES_KEY = 'roles';
export const RESOURCE_KEY = 'resource';
export const PERMISSION_KEY = 'permission';

export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);
export const RequiresResource = (resource: ResourceType) => SetMetadata(RESOURCE_KEY, resource);
export const RequiresPermission = (level: PermissionLevel) => SetMetadata(PERMISSION_KEY, level);

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredResource = this.reflector.getAllAndOverride<ResourceType>(RESOURCE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredPermission = this.reflector.getAllAndOverride<PermissionLevel>(PERMISSION_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles && !requiredResource) {
      return true; // No role or resource requirements
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      return false;
    }

    // Collect all roles the user has
    const userRoles: (BaseRole | ReviewerRole | ApproverRole)[] = [
      user.baseRole,
      user.reviewerRole,
      user.approverRole
    ].filter(Boolean);

    // Check role requirements if specified
    if (requiredRoles) {
      const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
      if (!hasRequiredRole) {
        return false;
      }
    }

    // Check resource permission if specified
    if (requiredResource && requiredPermission) {
      return hasPermission(userRoles, requiredResource, requiredPermission);
    }

    return true;
  }
}

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const resource = this.reflector.getAllAndOverride<ResourceType>(RESOURCE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredPermission = this.reflector.getAllAndOverride<PermissionLevel>(PERMISSION_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!resource || !requiredPermission) {
      return true; // No specific permission requirements
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      return false;
    }

    const userRoles = [user.baseRole, user.reviewerRole, user.approverRole].filter(Boolean);
    return hasPermission(userRoles, resource, requiredPermission);
  }
}

export const Auth = (roles: UserRole[] = [], resource?: ResourceType, permission?: PermissionLevel) => {
  return applyDecorators(
    SetMetadata(ROLES_KEY, roles),
    ...(resource ? [SetMetadata(RESOURCE_KEY, resource)] : []),
    ...(permission ? [SetMetadata(PERMISSION_KEY, permission)] : [])
  );
};
import {
  Controller,
  Get,
  Query,
  UseGuards,
  ParseDatePipe,
  ParseIntPipe,
  CacheTTL,
  UseInterceptors,
  CacheInterceptor,
} from '@nestjs/common';
import { AdminDashboardService } from './admin-dashboard.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard, Roles } from '../auth/guards/roles.guard';
import { UserRole } from '@prisma/client';

@Controller('admin/dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class AdminDashboardController {
  constructor(private readonly dashboardService: AdminDashboardService) {}

  @Get('overview')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300) // Cache for 5 minutes
  async getDashboardOverview() {
    const [
      userStats,
      activityMetrics,
      systemHealth,
      performanceMetrics,
    ] = await Promise.all([
      this.dashboardService.getUserStatistics(),
      this.dashboardService.getActivityMetrics(),
      this.dashboardService.getSystemHealth(),
      this.dashboardService.getPerformanceMetrics(),
    ]);

    return {
      userStats,
      activityMetrics,
      systemHealth,
      performanceMetrics,
    };
  }

  @Get('users/statistics')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getUserStatistics() {
    return this.dashboardService.getUserStatistics();
  }

  @Get('activity/metrics')
  async getActivityMetrics(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    return this.dashboardService.getActivityMetrics(startDate, endDate);
  }

  @Get('system/health')
  async getSystemHealth() {
    return this.dashboardService.getSystemHealth();
  }

  @Get('performance/metrics')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getPerformanceMetrics() {
    return this.dashboardService.getPerformanceMetrics();
  }

  @Get('reviews/statistics')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getReviewStatistics(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    const metrics = await this.dashboardService.getActivityMetrics(startDate, endDate);
    return metrics.reviewActivity;
  }

  @Get('approvals/statistics')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getApprovalStatistics(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    const metrics = await this.dashboardService.getActivityMetrics(startDate, endDate);
    return metrics.approvalActivity;
  }

  @Get('users/active')
  async getActiveUsers(
    @Query('period') period: 'daily' | 'weekly' | 'monthly' = 'daily',
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    const metrics = await this.dashboardService.getActivityMetrics(startDate, endDate);
    return metrics.loginActivity;
  }

  @Get('applications/throughput')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getApplicationThroughput(
    @Query('period') period: 'daily' | 'weekly' | 'monthly' = 'daily',
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    const metrics = await this.dashboardService.getPerformanceMetrics();
    return metrics.applicationThroughput;
  }

  @Get('users/productivity')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getUserProductivity(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
    @Query('department') department?: string,
    @Query('role') role?: string,
  ) {
    const metrics = await this.dashboardService.getPerformanceMetrics();
    return metrics.userProductivity;
  }

  @Get('system/errors')
  async getSystemErrors(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
    @Query('severity') severity?: string,
  ) {
    const health = await this.dashboardService.getSystemHealth();
    return health.recentErrors;
  }

  @Get('departments/performance')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getDepartmentPerformance(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    const metrics = await this.dashboardService.getPerformanceMetrics();
    return {
      reviewTimes: metrics.averageReviewTime,
      approvalTimes: metrics.averageApprovalTime,
      throughput: metrics.applicationThroughput,
    };
  }

  @Get('roles/activity')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getRoleActivity(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    const [userStats, activityMetrics] = await Promise.all([
      this.dashboardService.getUserStatistics(),
      this.dashboardService.getActivityMetrics(startDate, endDate),
    ]);

    return {
      distribution: userStats.roleDistribution,
      activity: activityMetrics.userActions,
    };
  }

  @Get('notifications/statistics')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getNotificationStatistics(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    // This would be implemented in the service
    // Return notification delivery stats, read rates, etc.
    return {
      // Implementation pending
    };
  }

  @Get('audit/summary')
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(300)
  async getAuditSummary(
    @Query('startDate', ParseDatePipe) startDate?: Date,
    @Query('endDate', ParseDatePipe) endDate?: Date,
  ) {
    // This would be implemented in the service
    // Return summary of audit logs
    return {
      // Implementation pending
    };
  }
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUserService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcrypt");
let AdminUserService = class AdminUserService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createUser(dto) {
        const existingUser = await this.prisma.user.findUnique({
            where: { email: dto.email },
        });
        if (existingUser) {
            throw new common_1.ConflictException('Email already exists');
        }
        const hashedPassword = await bcrypt.hash(dto.password, 10);
        const user = await this.prisma.user.create({
            data: {
                firstName: dto.firstName,
                lastName: dto.lastName,
                email: dto.email,
                password: hashedPassword,
                baseRole: dto.baseRole,
                reviewerRole: dto.reviewerRole,
                approverRole: dto.approverRole,
                department: dto.department,
                organizationId: dto.organizationId,
                isActive: dto.isActive ?? true,
            },
        });
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
    }
    async findAllUsers(filters) {
        const where = {};
        if (filters.baseRoles?.length) {
            where.baseRole = { in: filters.baseRoles };
        }
        if (filters.reviewerRoles?.length) {
            where.reviewerRole = { in: filters.reviewerRoles };
        }
        if (filters.approverRoles?.length) {
            where.approverRole = { in: filters.approverRoles };
        }
        if (filters.search) {
            where.OR = [
                { firstName: { contains: filters.search, mode: 'insensitive' } },
                { lastName: { contains: filters.search, mode: 'insensitive' } },
                { email: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        if (filters.department) {
            where.department = filters.department;
        }
        if (filters.organizationId) {
            where.organizationId = filters.organizationId;
        }
        if (typeof filters.isActive === 'boolean') {
            where.isActive = filters.isActive;
        }
        const page = filters.page || 0;
        const limit = filters.limit || 10;
        const skip = page * limit;
        const orderBy = {};
        if (filters.sortBy) {
            orderBy[filters.sortBy] = filters.sortOrder || 'desc';
        }
        else {
            orderBy.createdAt = 'desc';
        }
        const [total, users] = await Promise.all([
            this.prisma.user.count({ where }),
            this.prisma.user.findMany({
                where,
                skip,
                take: limit,
                orderBy,
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    baseRole: true,
                    reviewerRole: true,
                    approverRole: true,
                    department: true,
                    organizationId: true,
                    isActive: true,
                    lastLoginAt: true,
                    createdAt: true,
                    updatedAt: true,
                    organization: {
                        select: {
                            name: true,
                        },
                    },
                },
            }),
        ]);
        return {
            data: users,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                baseRole: true,
                reviewerRole: true,
                approverRole: true,
                department: true,
                organizationId: true,
                isActive: true,
                lastLoginAt: true,
                createdAt: true,
                updatedAt: true,
                organization: {
                    select: {
                        name: true,
                    },
                },
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async updateUser(id, dto) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (dto.email && dto.email !== user.email) {
            const existingUser = await this.prisma.user.findUnique({
                where: { email: dto.email },
            });
            if (existingUser) {
                throw new common_1.ConflictException('Email already exists');
            }
        }
        const updatedUser = await this.prisma.user.update({
            where: { id },
            data: dto,
        });
        const { password, ...userWithoutPassword } = updatedUser;
        return userWithoutPassword;
    }
    async assignRoles(id, dto) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const updatedUser = await this.prisma.user.update({
            where: { id },
            data: {
                baseRole: dto.baseRole || user.baseRole,
                reviewerRole: dto.reviewerRole,
                approverRole: dto.approverRole,
            },
        });
        const { password, ...userWithoutPassword } = updatedUser;
        return userWithoutPassword;
    }
    async getUserActivity(dto) {
        const where = {
            userId: dto.userId,
        };
        if (dto.startDate || dto.endDate) {
            where.createdAt = {};
            if (dto.startDate)
                where.createdAt.gte = new Date(dto.startDate);
            if (dto.endDate)
                where.createdAt.lte = new Date(dto.endDate);
        }
        if (dto.activityType) {
            where.type = dto.activityType;
        }
        const page = dto.page || 0;
        const limit = dto.limit || 10;
        const skip = page * limit;
        const [total, activities] = await Promise.all([
            this.prisma.activityLog.count({ where }),
            this.prisma.activityLog.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
            }),
        ]);
        return {
            data: activities,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async bulkUpdateUsers(dto) {
        const users = await this.prisma.user.findMany({
            where: {
                id: { in: dto.userIds },
            },
        });
        if (users.length !== dto.userIds.length) {
            throw new common_1.BadRequestException('Some user IDs are invalid');
        }
        const updateData = {};
        if (typeof dto.isActive === 'boolean')
            updateData.isActive = dto.isActive;
        if (dto.baseRole)
            updateData.baseRole = dto.baseRole;
        if (dto.reviewerRole)
            updateData.reviewerRole = dto.reviewerRole;
        if (dto.approverRole)
            updateData.approverRole = dto.approverRole;
        await this.prisma.user.updateMany({
            where: {
                id: { in: dto.userIds },
            },
            data: updateData,
        });
        return this.prisma.user.findMany({
            where: {
                id: { in: dto.userIds },
            },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                baseRole: true,
                reviewerRole: true,
                approverRole: true,
                department: true,
                isActive: true,
            },
        });
    }
    async resetUserPassword(id, newPassword) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await this.prisma.user.update({
            where: { id },
            data: {
                password: hashedPassword,
                passwordChangedAt: new Date(),
            },
        });
        return { message: 'Password reset successfully' };
    }
    async deactivateUser(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const updatedUser = await this.prisma.user.update({
            where: { id },
            data: {
                isActive: false,
            },
        });
        const { password, ...userWithoutPassword } = updatedUser;
        return userWithoutPassword;
    }
    async reactivateUser(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const updatedUser = await this.prisma.user.update({
            where: { id },
            data: {
                isActive: true,
            },
        });
        const { password, ...userWithoutPassword } = updatedUser;
        return userWithoutPassword;
    }
};
exports.AdminUserService = AdminUserService;
exports.AdminUserService = AdminUserService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AdminUserService);
//# sourceMappingURL=admin-user.service.js.map
import { PrismaService } from '../prisma/prisma.service';
import { CreateObjectiveDto, UpdateObjectiveDto } from './dto/create-objective.dto';
export declare class ObjectivesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createObjectiveDto: CreateObjectiveDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }[]>;
    findOne(id: number): Promise<{
        parties: ({
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            duration: number;
            partyId: string;
            responsibilityId: number | null;
            objectiveId: number | null;
            goalId: number;
            signatory: string;
            position: string;
            reasonForExtendedDuration: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    update(id: number, updateObjectiveDto: UpdateObjectiveDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
}

import { PrismaService } from '../prisma/prisma.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from './dto';
export declare class OrganizationsService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(currentUserId: string): Promise<({
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    })[]>;
    findOne(id: string, currentUserId: string): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    }>;
    create(createOrganizationDto: CreateOrganizationDto, currentUserId: string): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    }>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, currentUserId: string): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationId: number;
            province: string | null;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            district: string | null;
            street: string | null;
            poBox: string | null;
            postalCode: string | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
        centralLevelInstitutionId: number | null;
    }>;
    remove(id: string, currentUserId: string): Promise<{
        message: string;
    }>;
    private validateOrganizationData;
    private validateAddresses;
}

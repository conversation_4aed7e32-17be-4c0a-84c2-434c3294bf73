import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { NotificationService } from './notification.service';
import { JwtAuthGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';

@WebSocketGateway({
  namespace: '/notifications',
  cors: {
    origin: process.env.CLIENT_URL,
    credentials: true
  }
})
@Controller('notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
export class NotificationController implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private userSockets: Map<number, string[]> = new Map();

  constructor(private readonly notificationService: NotificationService) {}

  // WebSocket connection handling
  async handleConnection(client: Socket) {
    const token = client.handshake.auth.token;
    if (!token) {
      client.disconnect();
      return;
    }

    try {
      // Verify token and get user ID
      const userId = this.getUserIdFromToken(token);

      // Store socket connection
      const userSockets = this.userSockets.get(userId) || [];
      userSockets.push(client.id);
      this.userSockets.set(userId, userSockets);

      // Join user-specific room
      client.join(`user-${userId}`);

      // Send initial unread count
      const unreadCount = await this.notificationService.getUnreadCount(userId);
      client.emit('unreadCount', unreadCount);

    } catch (error) {
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    // Remove socket from userSockets map
    for (const [userId, sockets] of this.userSockets.entries()) {
      const index = sockets.indexOf(client.id);
      if (index !== -1) {
        sockets.splice(index, 1);
        if (sockets.length === 0) {
          this.userSockets.delete(userId);
        } else {
          this.userSockets.set(userId, sockets);
        }
        break;
      }
    }
  }

  // HTTP endpoints
  @Get()
  async getUserNotifications(
    @Request() req,
    @Query('skip', ParseIntPipe) skip: number = 0,
    @Query('take', ParseIntPipe) take: number = 10,
    @Query('includeRead') includeRead: boolean = false
  ) {
    return this.notificationService.getUserNotifications(req.user.id, {
      skip,
      take,
      includeRead
    });
  }

  @Get('unread-count')
  async getUnreadCount(@Request() req) {
    return {
      count: await this.notificationService.getUnreadCount(req.user.id)
    };
  }

  @Post(':id/mark-read')
  async markAsRead(
    @Param('id', ParseIntPipe) id: number,
    @Request() req
  ) {
    await this.notificationService.markNotificationAsRead(id, req.user.id);

    // Update unread count via WebSocket
    const unreadCount = await this.notificationService.getUnreadCount(req.user.id);
    this.server.to(`user-${req.user.id}`).emit('unreadCount', unreadCount);

    return { success: true };
  }

  @Post('mark-all-read')
  async markAllRead(@Request() req) {
    // Implementation for marking all notifications as read
    // This would be implemented in the notification service
    const notifications = await this.notificationService.getUserNotifications(req.user.id, {
      includeRead: false
    });

    for (const notification of notifications) {
      await this.notificationService.markNotificationAsRead(notification.id, req.user.id);
    }

    // Update unread count via WebSocket
    this.server.to(`user-${req.user.id}`).emit('unreadCount', 0);

    return { success: true };
  }

  // WebSocket subscriptions
  @SubscribeMessage('subscribe')
  handleSubscribe(client: Socket) {
    const userId = this.getUserIdFromSocket(client);
    if (userId) {
      client.join(`user-${userId}`);
    }
  }

  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(client: Socket) {
    const userId = this.getUserIdFromSocket(client);
    if (userId) {
      client.leave(`user-${userId}`);
    }
  }

  // Helper methods for sending notifications
  async sendNotificationToUser(userId: number, notification: any) {
    // Store notification in database
    const storedNotification = await this.notificationService.createNotification({
      userId,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      applicationId: notification.applicationId,
    });

    // Send notification via WebSocket
    this.server.to(`user-${userId}`).emit('notification', storedNotification);

    // Update unread count
    const unreadCount = await this.notificationService.getUnreadCount(userId);
    this.server.to(`user-${userId}`).emit('unreadCount', unreadCount);
  }

  async sendNotificationToRole(role: string, notification: any) {
    // Find all users with the specified role
    const users = await this.getUsersByRole(role);

    // Send notification to each user
    for (const user of users) {
      await this.sendNotificationToUser(user.id, notification);
    }
  }

  // Helper methods
  private getUserIdFromToken(token: string): number {
    // Implement JWT token verification and user ID extraction
    // This should use your actual JWT verification logic
    return 0; // Placeholder
  }

  private getUserIdFromSocket(client: Socket): number {
    // Implement user ID extraction from socket
    // This should use your actual socket authentication logic
    return 0; // Placeholder
  }

  private async getUsersByRole(role: string) {
    // Implement user lookup by role
    // This should use your actual user service or repository
    return []; // Placeholder
  }
}
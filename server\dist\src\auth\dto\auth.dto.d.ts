import { UserRole } from '@prisma/client';
import { ReviewerRole, ApproverRole } from '../../../common/enums/roles.enum';
export declare class RegisterUserDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    baseRole: UserRole;
    reviewerRole?: ReviewerRole;
    approverRole?: ApproverRole;
    department?: string;
    organizationId?: number;
}
export declare class LoginDto {
    email: string;
    password: string;
}
export declare class ChangePasswordDto {
    currentPassword: string;
    newPassword: string;
}
export declare class ResetPasswordDto {
    token: string;
    newPassword: string;
}
export declare class ForgotPasswordDto {
    email: string;
}

import { UserRole, ReviewerRole, ApproverRole } from '@prisma/client';
export declare class CreateUserDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    baseRole: UserRole;
    reviewerRole?: ReviewerRole;
    approverRole?: ApproverRole;
    department?: string;
    organizationId?: number;
    isActive?: boolean;
}
export declare class UpdateUserDto {
    firstName?: string;
    lastName?: string;
    email?: string;
    baseRole?: UserRole;
    reviewerRole?: ReviewerRole;
    approverRole?: ApproverRole;
    department?: string;
    organizationId?: number;
    isActive?: boolean;
}
export declare class AssignRolesDto {
    baseRole?: UserRole;
    reviewerRole?: ReviewerRole;
    approverRole?: ApproverRole;
}
export declare class FilterUsersDto {
    baseRoles?: UserRole[];
    reviewerRoles?: ReviewerRole[];
    approverRoles?: ApproverRole[];
    search?: string;
    department?: string;
    organizationId?: number;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class UserActivityDto {
    userId: number;
    startDate?: string;
    endDate?: string;
    activityType?: string;
    page?: number;
    limit?: number;
}
export declare class BulkUserActionDto {
    userIds: number[];
    isActive?: boolean;
    baseRole?: UserRole;
    reviewerRole?: ReviewerRole;
    approverRole?: ApproverRole;
}

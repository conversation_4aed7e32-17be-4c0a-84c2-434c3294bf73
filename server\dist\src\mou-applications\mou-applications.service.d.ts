import { PrismaService } from '../prisma/prisma.service';
import { CreateMouApplicationDto, UpdateMouApplicationDto } from './dto/create-mou-application.dto';
export declare class MouApplicationsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createMouApplicationDto: CreateMouApplicationDto): Promise<{
        user: {
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            firstName: string;
            lastName: string;
            email: string;
            password: string;
            emailVerified: boolean;
            verifiedAt: Date | null;
            verificationToken: string | null;
            verificationTokenExpiryTime: Date | null;
            refreshToken: string | null;
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            invitationToken: string | null;
            invitationExpires: Date | null;
            invitedBy: string | null;
            role: import("@prisma/client").$Enums.UserRole;
            isActive: boolean;
            organizationId: number | null;
        };
        mou: {
            party: {
                organization: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    organizationName: string;
                    organizationPhoneNumber: string;
                    organizationEmail: string;
                    organizationWebsite: string | null;
                    homeCountryRepresentative: string;
                    rwandaRepresentative: string;
                    organizationRgbNumber: string;
                    organizationTypeId: number;
                    centralLevelInstitutionId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                duration: number;
                partyId: string;
                responsibilityId: number | null;
                objectiveId: number | null;
                goalId: number;
                signatory: string;
                position: string;
                reasonForExtendedDuration: string | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
        projects: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        }[];
        approvalSteps: ({
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        userId: number | null;
        applicationKey: string;
        mouId: number;
    }>;
    findAll(page?: number, limit?: number, userId?: number): Promise<{
        data: ({
            user: {
                organization: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    organizationName: string;
                    organizationPhoneNumber: string;
                    organizationEmail: string;
                    organizationWebsite: string | null;
                    homeCountryRepresentative: string;
                    rwandaRepresentative: string;
                    organizationRgbNumber: string;
                    organizationTypeId: number;
                    centralLevelInstitutionId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
            mou: {
                party: {
                    organization: {
                        id: number;
                        createdAt: Date;
                        updatedAt: Date;
                        deleted: boolean;
                        organizationName: string;
                        organizationPhoneNumber: string;
                        organizationEmail: string;
                        organizationWebsite: string | null;
                        homeCountryRepresentative: string;
                        rwandaRepresentative: string;
                        organizationRgbNumber: string;
                        organizationTypeId: number;
                        centralLevelInstitutionId: number | null;
                    };
                } & {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    organizationId: number;
                    duration: number;
                    partyId: string;
                    responsibilityId: number | null;
                    objectiveId: number | null;
                    goalId: number;
                    signatory: string;
                    position: string;
                    reasonForExtendedDuration: string | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                mouKey: string;
                partyId: number;
            };
            projects: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            }[];
            approvalSteps: ({
                reviewer: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    firstName: string;
                    lastName: string;
                    email: string;
                    password: string;
                    emailVerified: boolean;
                    verifiedAt: Date | null;
                    verificationToken: string | null;
                    verificationTokenExpiryTime: Date | null;
                    refreshToken: string | null;
                    passwordResetToken: string | null;
                    passwordResetExpires: Date | null;
                    invitationToken: string | null;
                    invitationExpires: Date | null;
                    invitedBy: string | null;
                    role: import("@prisma/client").$Enums.UserRole;
                    isActive: boolean;
                    organizationId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                role: import("@prisma/client").$Enums.UserRole;
                mouApplicationId: number;
                projectId: number | null;
                reviewerId: number;
                status: import("@prisma/client").$Enums.ApprovalStatusType;
                comment: string | null;
            })[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            userId: number | null;
            applicationKey: string;
            mouId: number;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<{
        user: {
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            firstName: string;
            lastName: string;
            email: string;
            password: string;
            emailVerified: boolean;
            verifiedAt: Date | null;
            verificationToken: string | null;
            verificationTokenExpiryTime: Date | null;
            refreshToken: string | null;
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            invitationToken: string | null;
            invitationExpires: Date | null;
            invitedBy: string | null;
            role: import("@prisma/client").$Enums.UserRole;
            isActive: boolean;
            organizationId: number | null;
        };
        mou: {
            party: {
                organization: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    organizationName: string;
                    organizationPhoneNumber: string;
                    organizationEmail: string;
                    organizationWebsite: string | null;
                    homeCountryRepresentative: string;
                    rwandaRepresentative: string;
                    organizationRgbNumber: string;
                    organizationTypeId: number;
                    centralLevelInstitutionId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                duration: number;
                partyId: string;
                responsibilityId: number | null;
                objectiveId: number | null;
                goalId: number;
                signatory: string;
                position: string;
                reasonForExtendedDuration: string | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
        projects: ({
            budgetType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            fundingUnit: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                unitName: string;
            };
            fundingSource: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                sourceName: string;
            };
            activities: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                description: string | null;
                projectId: number;
                startDate: Date;
                endDate: Date;
                implementer: string;
                implementerUnit: string;
                fiscalYear: number;
                domainInterventionId: number;
                inputId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        })[];
        approvalSteps: ({
            project: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        userId: number | null;
        applicationKey: string;
        mouId: number;
    }>;
    findByApplicationKey(applicationKey: string): Promise<{
        user: {
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            firstName: string;
            lastName: string;
            email: string;
            password: string;
            emailVerified: boolean;
            verifiedAt: Date | null;
            verificationToken: string | null;
            verificationTokenExpiryTime: Date | null;
            refreshToken: string | null;
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            invitationToken: string | null;
            invitationExpires: Date | null;
            invitedBy: string | null;
            role: import("@prisma/client").$Enums.UserRole;
            isActive: boolean;
            organizationId: number | null;
        };
        mou: {
            party: {
                organization: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    organizationName: string;
                    organizationPhoneNumber: string;
                    organizationEmail: string;
                    organizationWebsite: string | null;
                    homeCountryRepresentative: string;
                    rwandaRepresentative: string;
                    organizationRgbNumber: string;
                    organizationTypeId: number;
                    centralLevelInstitutionId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                duration: number;
                partyId: string;
                responsibilityId: number | null;
                objectiveId: number | null;
                goalId: number;
                signatory: string;
                position: string;
                reasonForExtendedDuration: string | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
        projects: ({
            activities: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                description: string | null;
                projectId: number;
                startDate: Date;
                endDate: Date;
                implementer: string;
                implementerUnit: string;
                fiscalYear: number;
                domainInterventionId: number;
                inputId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        })[];
        approvalSteps: ({
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        userId: number | null;
        applicationKey: string;
        mouId: number;
    }>;
    update(id: number, updateMouApplicationDto: UpdateMouApplicationDto): Promise<{
        user: {
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            firstName: string;
            lastName: string;
            email: string;
            password: string;
            emailVerified: boolean;
            verifiedAt: Date | null;
            verificationToken: string | null;
            verificationTokenExpiryTime: Date | null;
            refreshToken: string | null;
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            invitationToken: string | null;
            invitationExpires: Date | null;
            invitedBy: string | null;
            role: import("@prisma/client").$Enums.UserRole;
            isActive: boolean;
            organizationId: number | null;
        };
        mou: {
            party: {
                organization: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    organizationName: string;
                    organizationPhoneNumber: string;
                    organizationEmail: string;
                    organizationWebsite: string | null;
                    homeCountryRepresentative: string;
                    rwandaRepresentative: string;
                    organizationRgbNumber: string;
                    organizationTypeId: number;
                    centralLevelInstitutionId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                duration: number;
                partyId: string;
                responsibilityId: number | null;
                objectiveId: number | null;
                goalId: number;
                signatory: string;
                position: string;
                reasonForExtendedDuration: string | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
        projects: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        }[];
        approvalSteps: ({
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        userId: number | null;
        applicationKey: string;
        mouId: number;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getApplicationStats(userId?: number): Promise<{
        total: number;
        pending: number;
        approved: number;
        rejected: number;
    }>;
}

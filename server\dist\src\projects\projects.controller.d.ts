import { ProjectsService } from './projects.service';
import { CreateProjectDto, UpdateProjectDto } from './dto/create-project.dto';
export declare class ProjectsController {
    private readonly projectsService;
    constructor(projectsService: ProjectsService);
    create(createProjectDto: CreateProjectDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    findAll(page?: string, limit?: string, mouApplicationId?: string, organizationId?: string): Promise<{
        data: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        }[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getProjectsByMouApplication(mouApplicationId: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    update(id: number, updateProjectDto: UpdateProjectDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
}

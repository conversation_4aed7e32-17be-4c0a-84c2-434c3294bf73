import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { UserRole } from '@prisma/client';
import { ReviewerRole, ApproverRole } from '../../../common/enums/roles.enum';

export class RegisterUserDto {
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;

  @IsEnum(UserRole)
  baseRole: UserRole;

  @IsEnum(ReviewerRole)
  @IsOptional()
  reviewerRole?: ReviewerRole;

  @IsEnum(ApproverRole)
  @IsOptional()
  approverRole?: ApproverRole;

  @IsString()
  @IsOptional()
  department?: string;

  @IsOptional()
  organizationId?: number;
}

export class LoginDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;
}

export class ChangePasswordDto {
  @IsString()
  @MinLength(8)
  currentPassword: string;

  @IsString()
  @MinLength(8)
  newPassword: string;
}

export class ResetPasswordDto {
  @IsString()
  token: string;

  @IsString()
  @MinLength(8)
  newPassword: string;
}

export class ForgotPasswordDto {
  @IsEmail()
  email: string;
}

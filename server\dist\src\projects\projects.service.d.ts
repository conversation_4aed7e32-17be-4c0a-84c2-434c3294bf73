import { PrismaService } from '../prisma/prisma.service';
import { CreateProjectDto, UpdateProjectDto } from './dto/create-project.dto';
export declare class ProjectsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createProjectDto: CreateProjectDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    findAll(page?: number, limit?: number, mouApplicationId?: number, organizationId?: number): Promise<{
        data: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        }[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    update(id: number, updateProjectDto: UpdateProjectDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getProjectsByMouApplication(mouApplicationId: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }[]>;
    private validateRelationships;
}

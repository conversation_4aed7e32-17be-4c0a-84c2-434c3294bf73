import { ApiProperty } from '@nestjs/swagger';
import { BaseRole, ReviewerRole, ApproverRole } from '../../common/enums/roles.enum';

export class UserResponse {
  @ApiProperty()
  id: number;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty()
  email: string;

  @ApiProperty({ enum: BaseRole })
  baseRole: BaseRole;

  @ApiProperty({ enum: ReviewerRole, nullable: true })
  reviewerRole?: ReviewerRole;

  @ApiProperty({ enum: ApproverRole, nullable: true })
  approverRole?: ApproverRole;

  @ApiProperty({ nullable: true })
  department?: string;

  @ApiProperty({ nullable: true })
  organizationId?: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty({ nullable: true })
  lastLoginAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class UserListResponse {
  @ApiProperty({ type: [UserResponse] })
  data: UserResponse[];

  @ApiProperty()
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export class ActivityLogEntry {
  @ApiProperty()
  id: number;

  @ApiProperty()
  userId: number;

  @ApiProperty()
  action: string;

  @ApiProperty()
  details: Record<string, any>;

  @ApiProperty()
  category: string;

  @ApiProperty()
  importance: string;

  @ApiProperty({ nullable: true })
  ipAddress?: string;

  @ApiProperty({ nullable: true })
  userAgent?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  user: {
    firstName: string;
    lastName: string;
    email: string;
  };
}

export class ActivityResponse {
  @ApiProperty({ type: [ActivityLogEntry] })
  data: ActivityLogEntry[];

  @ApiProperty()
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export class AuditLogEntry {
  @ApiProperty()
  id: number;

  @ApiProperty()
  userId: number;

  @ApiProperty()
  action: string;

  @ApiProperty()
  target: string;

  @ApiProperty()
  changes: Record<string, any>;

  @ApiProperty()
  timestamp: Date;

  @ApiProperty()
  user: {
    firstName: string;
    lastName: string;
    email: string;
    baseRole: BaseRole;
  };
}

export class AuditLogResponse {
  @ApiProperty({ type: [AuditLogEntry] })
  data: AuditLogEntry[];

  @ApiProperty()
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export class PermissionSet {
  @ApiProperty()
  canCreate: boolean;

  @ApiProperty()
  canRead: boolean;

  @ApiProperty()
  canUpdate: boolean;

  @ApiProperty()
  canDelete: boolean;

  @ApiProperty()
  canApprove: boolean;

  @ApiProperty()
  canReview: boolean;

  @ApiProperty({ type: 'object', additionalProperties: true })
  custom: Record<string, boolean>;
}

export class UserPermissions {
  @ApiProperty()
  userId: number;

  @ApiProperty({
    type: 'object',
    additionalProperties: {
      type: 'object',
      $ref: '#/components/schemas/PermissionSet'
    }
  })
  permissions: Record<string, PermissionSet>;
}
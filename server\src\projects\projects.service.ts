import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProjectDto, UpdateProjectDto } from './dto/create-project.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ProjectsService {
  constructor(private prisma: PrismaService) {}

  async create(createProjectDto: CreateProjectDto) {
    try {
      // Validate foreign key relationships
      await this.validateRelationships(createProjectDto);

      // Check if project document is already used
      const existingProject = await this.prisma.project.findUnique({
        where: { projectDocumentId: createProjectDto.projectDocumentId },
      });

      if (existingProject) {
        throw new ConflictException(`Document with ID ${createProjectDto.projectDocumentId} is already used by another project`);
      }

      const project = await this.prisma.project.create({
        data: createProjectDto,
        include: {
          budgetType: true,
          fundingUnit: true,
          fundingSource: true,
          organization: true,
          projectDocument: true,
          mouApplication: true,
          activities: true,
          approvalSteps: {
            include: {
              reviewer: true,
            },
          },
        },
      });

      return project;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Project document must be unique');
        }
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, mouApplicationId?: number, organizationId?: number) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.ProjectWhereInput = {
      deleted: false,
      ...(mouApplicationId && { mouApplicationId }),
      ...(organizationId && { organizationId }),
    };

    const [projects, total] = await Promise.all([
      this.prisma.project.findMany({
        where,
        skip,
        take: limit,
        include: {
          budgetType: true,
          fundingUnit: true,
          fundingSource: true,
          organization: true,
          projectDocument: true,
          mouApplication: true,
          activities: true,
          approvalSteps: {
            include: {
              reviewer: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.project.count({ where }),
    ]);

    return {
      data: projects,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const project = await this.prisma.project.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        budgetType: true,
        fundingUnit: true,
        fundingSource: true,
        organization: true,
        projectDocument: true,
        mouApplication: {
          include: {
            mou: {
              include: {
                party: true,
              },
            },
          },
        },
        activities: {
          include: {
            domainIntervention: true,
            input: {
              include: {
                inputSubclass: true,
              },
            },
          },
        },
        approvalSteps: {
          include: {
            reviewer: true,
          },
        },
      },
    });

    if (!project) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }

    return project;
  }

  async update(id: number, updateProjectDto: UpdateProjectDto) {
    // Check if project exists
    const existingProject = await this.prisma.project.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingProject) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }

    // Validate foreign key relationships if they are being updated
    if (Object.keys(updateProjectDto).some(key => 
      ['budgetTypeId', 'fundingUnitId', 'fundingSourceId', 'organizationId', 'projectDocumentId'].includes(key)
    )) {
      await this.validateRelationships({
        ...existingProject,
        ...updateProjectDto,
      } as CreateProjectDto);
    }

    // Check if project document is already used (if being updated)
    if (updateProjectDto.projectDocumentId && updateProjectDto.projectDocumentId !== existingProject.projectDocumentId) {
      const documentInUse = await this.prisma.project.findUnique({
        where: { projectDocumentId: updateProjectDto.projectDocumentId },
      });

      if (documentInUse) {
        throw new ConflictException(`Document with ID ${updateProjectDto.projectDocumentId} is already used by another project`);
      }
    }

    try {
      const updatedProject = await this.prisma.project.update({
        where: { id },
        data: updateProjectDto,
        include: {
          budgetType: true,
          fundingUnit: true,
          fundingSource: true,
          organization: true,
          projectDocument: true,
          mouApplication: true,
          activities: true,
          approvalSteps: {
            include: {
              reviewer: true,
            },
          },
        },
      });

      return updatedProject;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Project document must be unique');
        }
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if project exists
    const existingProject = await this.prisma.project.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingProject) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }

    // Soft delete
    await this.prisma.project.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Project deleted successfully' };
  }

  async getProjectsByMouApplication(mouApplicationId: number) {
    const projects = await this.prisma.project.findMany({
      where: {
        mouApplicationId,
        deleted: false,
      },
      include: {
        budgetType: true,
        fundingUnit: true,
        fundingSource: true,
        organization: true,
        projectDocument: true,
        activities: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return projects;
  }

  private async validateRelationships(dto: CreateProjectDto) {
    const [budgetType, fundingUnit, fundingSource, organization, document, mouApplication] = await Promise.all([
      this.prisma.budgetType.findUnique({ where: { id: dto.budgetTypeId } }),
      this.prisma.fundingUnit.findUnique({ where: { id: dto.fundingUnitId } }),
      this.prisma.fundingSource.findUnique({ where: { id: dto.fundingSourceId } }),
      this.prisma.organization.findUnique({ where: { id: dto.organizationId } }),
      this.prisma.document.findUnique({ where: { id: dto.projectDocumentId } }),
      this.prisma.mouApplication.findUnique({ where: { id: dto.mouApplicationId } }),
    ]);

    if (!budgetType) {
      throw new NotFoundException(`Budget type with ID ${dto.budgetTypeId} not found`);
    }
    if (!fundingUnit) {
      throw new NotFoundException(`Funding unit with ID ${dto.fundingUnitId} not found`);
    }
    if (!fundingSource) {
      throw new NotFoundException(`Funding source with ID ${dto.fundingSourceId} not found`);
    }
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${dto.organizationId} not found`);
    }
    if (!document) {
      throw new NotFoundException(`Document with ID ${dto.projectDocumentId} not found`);
    }
    if (!mouApplication) {
      throw new NotFoundException(`MoU application with ID ${dto.mouApplicationId} not found`);
    }
  }
}

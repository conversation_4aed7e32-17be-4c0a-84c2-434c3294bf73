import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDocumentDto, UpdateDocumentDto } from './dto/create-document.dto';
import { Prisma } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class DocumentsService {
  constructor(private prisma: PrismaService) {}

  async create(createDocumentDto: CreateDocumentDto) {
    try {
      // Validate foreign key relationships
      await this.validateRelationships(createDocumentDto);

      const document = await this.prisma.document.create({
        data: createDocumentDto,
        include: {
          organization: true,
          documentType: true,
          project: {
            include: {
              mouApplication: true,
            },
          },
        },
      });

      return document;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, organizationId?: number, documentTypeId?: number) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.DocumentWhereInput = {
      deleted: false,
      ...(organizationId && { organizationId }),
      ...(documentTypeId && { documentTypeId }),
    };

    const [documents, total] = await Promise.all([
      this.prisma.document.findMany({
        where,
        skip,
        take: limit,
        include: {
          organization: true,
          documentType: true,
          project: {
            include: {
              mouApplication: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.document.count({ where }),
    ]);

    return {
      data: documents,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const document = await this.prisma.document.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        organization: {
          include: {
            organizationType: true,
            addresses: true,
          },
        },
        documentType: true,
        project: {
          include: {
            mouApplication: {
              include: {
                mou: {
                  include: {
                    party: true,
                  },
                },
              },
            },
            activities: true,
          },
        },
      },
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async update(id: number, updateDocumentDto: UpdateDocumentDto) {
    // Check if document exists
    const existingDocument = await this.prisma.document.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingDocument) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    // Validate foreign key relationships if they are being updated
    if (updateDocumentDto.documentTypeId) {
      const documentType = await this.prisma.documentType.findUnique({
        where: { id: updateDocumentDto.documentTypeId },
      });

      if (!documentType) {
        throw new NotFoundException(`Document type with ID ${updateDocumentDto.documentTypeId} not found`);
      }
    }

    try {
      const updatedDocument = await this.prisma.document.update({
        where: { id },
        data: updateDocumentDto,
        include: {
          organization: true,
          documentType: true,
          project: {
            include: {
              mouApplication: true,
            },
          },
        },
      });

      return updatedDocument;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if document exists
    const existingDocument = await this.prisma.document.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingDocument) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    // Check if document is used by a project
    const projectUsingDocument = await this.prisma.project.findUnique({
      where: { projectDocumentId: id },
    });

    if (projectUsingDocument) {
      throw new ConflictException('Cannot delete document that is being used by a project');
    }

    // Soft delete
    await this.prisma.document.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Document deleted successfully' };
  }

  async getDocumentsByOrganization(organizationId: number) {
    const documents = await this.prisma.document.findMany({
      where: {
        organizationId,
        deleted: false,
      },
      include: {
        documentType: true,
        project: {
          include: {
            mouApplication: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return documents;
  }

  async getDocumentsByType(documentTypeId: number) {
    const documents = await this.prisma.document.findMany({
      where: {
        documentTypeId,
        deleted: false,
      },
      include: {
        organization: true,
        project: {
          include: {
            mouApplication: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return documents;
  }

  async uploadFile(file: Express.Multer.File, createDocumentDto: CreateDocumentDto) {
    try {
      // Validate file
      if (!file) {
        throw new BadRequestException('No file provided');
      }

      // Validate file type (you can customize this based on requirements)
      const allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'image/gif'
      ];

      if (!allowedMimeTypes.includes(file.mimetype)) {
        throw new BadRequestException('Invalid file type. Only PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, and GIF files are allowed');
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        throw new BadRequestException('File size exceeds 10MB limit');
      }

      // Create document record first
      const document = await this.create(createDocumentDto);

      // Generate unique filename
      const fileExtension = path.extname(file.originalname);
      const fileName = `doc_${document.id}_${Date.now()}${fileExtension}`;
      const uploadPath = path.join(process.cwd(), 'uploads', 'documents');
      const filePath = path.join(uploadPath, fileName);

      // Ensure upload directory exists
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }

      // Save file to disk
      fs.writeFileSync(filePath, file.buffer);

      // You might want to store file metadata in a separate table
      // For now, we'll return the document with file info
      return {
        document,
        file: {
          fileName,
          originalName: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          path: `/uploads/documents/${fileName}`,
          uploadedAt: new Date(),
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async downloadFile(id: number) {
    const document = await this.findOne(id);
    
    // In a real implementation, you would have file metadata stored
    // For now, we'll return a placeholder response
    throw new NotFoundException('File download functionality not implemented yet');
  }

  private async validateRelationships(dto: CreateDocumentDto) {
    const [organization, documentType] = await Promise.all([
      this.prisma.organization.findUnique({ where: { id: dto.organizationId } }),
      this.prisma.documentType.findUnique({ where: { id: dto.documentTypeId } }),
    ]);

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${dto.organizationId} not found`);
    }
    if (!documentType) {
      throw new NotFoundException(`Document type with ID ${dto.documentTypeId} not found`);
    }
  }
}

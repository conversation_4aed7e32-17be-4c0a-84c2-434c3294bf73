import { PrismaService } from '../prisma/prisma.service';
import { CreateDocumentDto, UpdateDocumentDto } from './dto/create-document.dto';
export declare class DocumentsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createDocumentDto: CreateDocumentDto): Promise<{
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        documentType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        project: {
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        projectId: number | null;
        documentTypeId: number;
    }>;
    findAll(page?: number, limit?: number, organizationId?: number, documentTypeId?: number): Promise<{
        data: ({
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
            documentType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            project: {
                mouApplication: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    userId: number | null;
                    applicationKey: string;
                    mouId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<{
        organization: {
            organizationType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            addresses: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationId: number;
                province: string | null;
                addressType: import("@prisma/client").$Enums.AddressType;
                country: string;
                district: string | null;
                street: string | null;
                poBox: string | null;
                postalCode: string | null;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        documentType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        project: {
            mouApplication: {
                mou: {
                    party: {
                        id: number;
                        createdAt: Date;
                        updatedAt: Date;
                        deleted: boolean;
                        name: string;
                        organizationId: number;
                        duration: number;
                        partyId: string;
                        responsibilityId: number | null;
                        objectiveId: number | null;
                        goalId: number;
                        signatory: string;
                        position: string;
                        reasonForExtendedDuration: string | null;
                    };
                } & {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    mouKey: string;
                    partyId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
            activities: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                description: string | null;
                projectId: number;
                startDate: Date;
                endDate: Date;
                implementer: string;
                implementerUnit: string;
                fiscalYear: number;
                domainInterventionId: number;
                inputId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        projectId: number | null;
        documentTypeId: number;
    }>;
    update(id: number, updateDocumentDto: UpdateDocumentDto): Promise<{
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        documentType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        project: {
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        projectId: number | null;
        documentTypeId: number;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getDocumentsByOrganization(organizationId: number): Promise<({
        documentType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        project: {
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        projectId: number | null;
        documentTypeId: number;
    })[]>;
    getDocumentsByType(documentTypeId: number): Promise<({
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        project: {
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        projectId: number | null;
        documentTypeId: number;
    })[]>;
    uploadFile(file: Express.Multer.File, createDocumentDto: CreateDocumentDto): Promise<{
        document: {
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
            documentType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            project: {
                mouApplication: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    userId: number | null;
                    applicationKey: string;
                    mouId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        };
        file: {
            fileName: string;
            originalName: string;
            size: number;
            mimeType: string;
            path: string;
            uploadedAt: Date;
        };
    }>;
    downloadFile(id: number): Promise<void>;
    private validateRelationships;
}

import { PrismaService } from '../prisma/prisma.service';
export declare class AdminDashboardService {
    private prisma;
    constructor(prisma: PrismaService);
    getUserStatistics(): Promise<{
        totalUsers: any;
        activeUsers: any;
        inactiveUsers: any;
        recentlyActive: any;
        roleDistribution: any;
        departmentDistribution: any;
    }>;
    getActivityMetrics(startDate?: Date, endDate?: Date): Promise<{
        loginActivity: any;
        userActions: any;
        reviewActivity: any;
        approvalActivity: any;
    }>;
    getSystemHealth(): Promise<{
        activeApplications: any;
        pendingReviews: any;
        pendingApprovals: any;
        recentErrors: any;
        systemStatus: {
            status: string;
            lastCheck: Date;
            components: {
                database: string;
                email: string;
                storage: string;
                notifications: string;
            };
        };
    }>;
    getPerformanceMetrics(): Promise<{
        averageReviewTime: number;
        averageApprovalTime: number;
        applicationThroughput: any;
        userProductivity: any;
    }>;
    private getRoleDistribution;
    private getDepartmentDistribution;
    private getLoginActivity;
    private getUserActions;
    private getReviewActivity;
    private getApprovalActivity;
    private calculateAverageReviewTime;
    private calculateAverageApprovalTime;
    private calculateApplicationThroughput;
    private calculateUserProductivity;
    private getDateFilter;
    private getSystemStatus;
}

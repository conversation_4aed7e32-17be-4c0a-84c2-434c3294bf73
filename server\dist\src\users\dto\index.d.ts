import { UserRole } from '../../auth/dto';
export declare class CreateUserDto {
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
}
export declare class UpdateUserDto {
    firstName?: string;
    lastName?: string;
    email?: string;
    role?: UserRole;
    organizationId?: string;
}
export declare class UserResponseDto {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
    emailVerified: boolean;
    organizationId?: string;
    createdAt: Date;
    updatedAt: Date;
}
